# Mandatory configs
kind: Job

jobConfigs:
  backoffLimit: 5
  activeDeadlineSeconds: 100
  parallelism: 1
  completions: 2
  suspend: false
 # ttlSecondsAfterFinished: 100

cronjobConfigs:
  schedule: "* * * * *"
  startingDeadlineSeconds: 100
  concurrencyPolicy: Allow
  suspend: false
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 1
  restartPolicy: OnFailure

kedaAutoscaling:
  envSourceContainerName: "" 
  minReplicaCount: 1 
  maxReplicaCount: 2
  pollingInterval: 30                         
  successfulJobsHistoryLimit: 5              
  failedJobsHistoryLimit: 5
  rolloutStrategy: default
  scalingStrategy:
    strategy: "custom"                        
    customScalingQueueLengthDeduction: 1      
    customScalingRunningJobPercentage: "0.5"  
    pendingPodConditions:                     
      - "Ready"
      - "PodScheduled"
      - "AnyOtherCustomPodCondition"
    multipleScalersCalculation : "max"
  triggers: 
  - type: rabbitmq
    metadata:
      queueName: hello
      host: RabbitMqHost
      queueLength  : '5'
    authenticationRef: {}
  triggerAuthentication:
    enabled: false
    name: ""
    spec: {}

MinReadySeconds: 60
GracePeriod: 30
image:
  pullPolicy: IfNotPresent
service:
  type: ClusterIP
  enabled: false
  #name: "service-1234567890"
  annotations: {}
    # test1: test2
    # test3: test4
ContainerPort:
  - name: app
    port: 8080
    servicePort: 80
    envoyPort: 8799
    useHTTP2: true
    supportStreaming: true
    idleTimeout: 1800s
#    servicemonitor:
#      enabled: true
#      path: /abc
#      scheme: 'http'
#      interval: 30s
#      scrapeTimeout: 20s
#      metricRelabelings:
#        - sourceLabels: [namespace]
#          regex: '(.*)'
#          replacement: myapp
#          targetLabel: target_namespace
resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  limits:
    cpu: 1
    memory: 200Mi
  requests:
    cpu: 0.10
    memory: 100Mi

# Optional configs

command:
  enabled: false
  value: []

args:
  enabled: false
  value:
    - /bin/sh
    - -c
    - touch /tmp/healthy; sleep 30; rm -rf /tmp/healthy; sleep 600

#For adding custom labels to pods

podLabels: {}
#  customKey: customValue
podAnnotations: {}
#  customKey: customValue

rawYaml: []

initContainers: []
  ## Additional init containers to run before the Scheduler pods.
  ## for example, be used to run a sidecar that chown Logs storage.
  #- name: volume-mount-hack
  #  image: busybox
  #  command: ["sh", "-c", "chown -R 1000:1000 logs"]
  #  volumeMounts:
  #    - mountPath: /usr/local/airflow/logs
  #      name: logs-data

containers: []
  ## Additional containers to run along with application pods.
  ## for example, be used to run a sidecar that chown Logs storage .
  #- name: volume-mount-hack
  #  image: busybox
  #  command: ["sh", "-c", "chown -R 1000:1000 logs"]
  #  volumeMounts:
  #    - mountPath: /usr/local/airflow/logs
  #      name: logs-data

volumeMounts: []
#     - name: log-volume
#       mountPath: /var/log

volumes: []
#     - name: log-volume
#       emptyDir: {}

tolerations: []

Spec:
  Affinity:
    Key:
    #  Key: kops.k8s.io/instancegroup
    Values:

prometheus:
  release: monitoring

server:
  deployment:
    image_tag: 1-95af053
    image: ""

servicemonitor:
  additionalLabels: {}

imagePullSecrets: []
  # - test1
  # - test2

containerSecurityContext:
  allowPrivilegeEscalation: false

podSecurityContext: {}
  # runAsUser: 1000
  # runAsGroup: 3000
  # fsGroup: 2000

shareProcessNamespace: false
setHostnameAsFQDN: false
readinessGates: []
ephemeralContainers: []
topologySpreadConstraints: []