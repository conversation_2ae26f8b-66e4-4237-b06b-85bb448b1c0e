{{- if and .Values.istio.enable .Values.istio.gateway.enabled -}}
apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: {{ template ".Chart.Name .fullname" $ }}-istio-gateway
  labels:
    app: {{ template ".Chart.Name .name" $ }}
    appId: {{ $.Values.app | quote }}
    envId: {{ $.Values.env | quote }}
    chart: {{ template ".Chart.Name .chart" $ }}
    release: {{ $.Release.Name }}
{{- if $.Values.appLabels }}
{{ toYaml $.Values.appLabels | indent 4 }}
{{- end }}
    {{- if $.Values.istio.gateway.labels }}
{{ toYaml $.Values.istio.gateway.labels | indent 4 }}
    {{- end }}
{{- if $.Values.istio.gateway.annotations }}
  annotations:
{{ toYaml $.Values.istio.gateway.annotations | indent 4 }}
{{- end }}
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts: 
      - {{ .Values.istio.gateway.host | quote -}}
{{ with .Values.istio.gateway }}
{{- if .tls.enabled }}
    tls:
      httpsRedirect: true
  - port:
      number: 443
      name: https
      protocol: HTTPS
    hosts:
      - {{ .host | quote }}
    tls:
      mode: SIMPLE
      credentialName: {{ .tls.secretName }}  
{{ end }}
{{ end }}
{{ end }}



