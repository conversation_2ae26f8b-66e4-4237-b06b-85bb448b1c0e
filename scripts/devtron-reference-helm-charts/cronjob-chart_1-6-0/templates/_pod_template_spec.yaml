{{ define "pod-template-spec" }}
{{- $hasCMEnvExists := false -}}
{{- $hasCMVolumeExists := false -}}
{{- if .Values.ConfigMaps.enabled }}
{{- range .Values.ConfigMaps.maps }}
{{- if eq .type "volume"}}
{{- $hasCMVolumeExists = true}}
{{- end }}
{{- if eq .type "environment"}}
{{- $hasCMEnvExists = true}}
{{- end }}
{{- end }}
{{- end }}

{{- $hasPVCExists := false -}}
{{- if .Values.persistentVolumeClaim.name }}
{{- $hasPVCExists = true }}
{{- end }}


{{- $hasSecretEnvExists := false -}}
{{- $hasSecretVolumeExists := false -}}
{{- if .Values.ConfigSecrets.enabled }}
{{- range .Values.ConfigSecrets.secrets }}
{{- if eq .type "volume"}}
{{- $hasSecretVolumeExists = true}}
{{- end }}
{{- if eq .type "environment"}}
{{- $hasSecretEnvExists = true}}
{{- end }}
{{- end }}
{{- end }}
{{- if $.Values.podExtraSpecs }}	
{{ toYaml $.Values.podExtraSpecs }}	
{{- end }}
{{- if $.Values.shareProcessNamespace }}
shareProcessNamespace: {{ $.Values.shareProcessNamespace }}
{{- end }}
{{- if $.Values.GracePeriod }}
terminationGracePeriodSeconds: {{ $.Values.GracePeriod }}
{{- end }}
{{- if $.Values.topologySpreadConstraints }}
topologySpreadConstraints:
{{- range $.Values.topologySpreadConstraints }}
- maxSkew: {{ .maxSkew }}
  topologyKey: {{ .topologyKey }}
  whenUnsatisfiable: {{ .whenUnsatisfiable }}
  labelSelector:
    matchLabels:
    {{- if and .autoLabelSelector .customLabelSelector }}
{{ toYaml .customLabelSelector | indent 6 }}
    {{- else if .autoLabelSelector }}
      app: {{ template ".Chart.Name .name" $ }}
      appId: {{ $.Values.app | quote }}
      envId: {{ $.Values.env | quote }}
      release: {{ $.Release.Name }}
    {{- else if .customLabelSelector }}
{{ toYaml .customLabelSelector | indent 6 }}
    {{- end }}
{{- end }}
{{- end }}
{{- if $.Values.podSpec.subdomain }}
subdomain: {{ $.Values.podSpec.subdomain }}
{{- end }}
{{- if $.Values.podSpec.setHostnameAsFQDN }}
setHostnameAsFQDN: {{ $.Values.podSpec.setHostnameAsFQDN }}
{{- end }}
{{- if $.Values.podSpec.schedulerName }}
schedulerName: {{ $.Values.podSpec.schedulerName }}
{{- end }}
{{- if $.Values.podSpec.readinessGates }}
readinessGates: 
  {{ toYaml $.podSpec.readinessGates }}
{{- end }}
{{- if $.Values.podSpec.dnsPolicy }}
dnsPolicy: {{ $.Values.podSpec.dnsPolicy }}
{{- end }}
{{- if $.Values.podSpec.enableServiceLinks }}
dnsPolicy: {{ $.Values.podSpec.enableServiceLinks }}
{{- end }}
{{- with $.Values.ephemeralContainers }}
ephemeralContainers:
{{- toYaml $.Values.ephemeralContainers }}
{{- end }}
{{- with $.Values.dnsConfig }}
dnsConfig:
{{- toYaml $.Values.dnsConfig }}
{{- end }}
restartPolicy: {{ $.Values.restartPolicy | default "OnFailure" }}
{{- if and $.Values.Spec.Affinity.Key $.Values.Spec.Affinity.Values }}
affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - key: {{ $.Values.Spec.Affinity.Key  }}
          operator: In
          values:
          - {{ $.Values.Spec.Affinity.Values | default "nodes"  }}
{{- end }}
{{- if $.Values.serviceAccountName }}
serviceAccountName: {{ $.Values.serviceAccountName }}
{{- end }}
{{- if .Values.tolerations }}
tolerations:
{{ toYaml .Values.tolerations | indent 2 }}
{{- end }}
{{- if $.Values.podSecurityContext }}
securityContext:
{{ toYaml .Values.podSecurityContext | indent 2 }}
{{- end }}  
{{- if $.Values.imagePullSecrets }}
imagePullSecrets:
{{- range .Values.imagePullSecrets }}
  - name: {{ . }}
{{- end }}
{{- end}}
{{- if $.Values.initContainers }}
initContainers:
{{- range $i, $c := .Values.initContainers }}
{{- if .reuseContainerImage }}
  - name: {{ $.Chart.Name }}-init-{{ add1 $i }}
    image: "{{ $.Values.server.deployment.image }}:{{ $.Values.server.deployment.image_tag }}"
    imagePullPolicy: {{ $.Values.image.pullPolicy }}
{{- if .command }}
    command:
{{ toYaml .command | indent 6 -}}
{{- end }}
{{- if .resources }}
    resources:
{{ toYaml .resources | indent 6 }}
{{- end }}
{{- if .volumeMounts }}
    volumeMounts:
{{ toYaml .volumeMounts | indent 6 -}}
{{- end }}
{{- else }}
  -
{{ toYaml $c | indent 4 -}}
{{- end }}
{{- end }}
{{- end }}
containers:
{{- if $.Values.containers }}
{{ toYaml $.Values.containers | indent 2 -}}
{{- end }}
  - name: {{ $.Chart.Name }}
    image: "{{ .Values.server.deployment.image }}:{{ .Values.server.deployment.image_tag }}"
    imagePullPolicy: {{ $.Values.image.pullPolicy }}
{{- if $.Values.containerExtraSpecs }}	
{{ toYaml .Values.containerExtraSpecs | indent 4 }}	
{{- end }}
{{- if $.Values.privileged }}
    securityContext:
      privileged: true
{{- end }}
{{- if $.Values.containerSecurityContext }}
    securityContext:
{{ toYaml .Values.containerSecurityContext | indent 6 }}
{{- end }}
{{- if and $.Values.containerSecurityContext $.Values.privileged }}
    securityContext:
      privileged: true
{{ toYaml .Values.containerSecurityContext | indent 6 }}
{{- end }}
    ports:
    {{- range $.Values.ContainerPort }}
      - name: {{ .name }}
        containerPort: {{ .port  }}
        protocol: TCP
    {{- end }}
{{- if and $.Values.command.value $.Values.command.enabled }}
    command:
{{ toYaml $.Values.command.value | indent 6 -}}
{{- end }}
{{- if and $.Values.args.value $.Values.args.enabled }}
    args:
{{ toYaml $.Values.args.value | indent 6 -}}
{{- end }}
    env:
      - name: CONFIG_HASH
        value: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
      - name: SECRET_HASH
        value: {{ include (print $.Template.BasePath "/secret.yaml") . | sha256sum }}
      - name: DEVTRON_APP_NAME
        value: {{ template ".Chart.Name .name" $ }}
      - name: POD_NAME
        valueFrom:
          fieldRef:
            fieldPath: metadata.name
    {{- range $.Values.EnvVariablesFromFieldPath }}
      - name: {{ .name }}
        valueFrom:
          fieldRef:
            fieldPath: {{ .fieldPath }}
    {{- end }}
    {{- range $.Values.EnvVariables }}
      - name: {{ .name}}
        value: {{ .value | quote }}
    {{- end }}
    {{- if or (and ($hasCMEnvExists) (.Values.ConfigMaps.enabled)) (and ($hasSecretEnvExists) (.Values.ConfigSecrets.enabled)) }}
    envFrom:
    {{- if .Values.ConfigMaps.enabled }}
    {{- range .Values.ConfigMaps.maps }}
    {{- if eq .type "environment" }}
    - configMapRef:
        {{- if eq .external true }}
        name: {{ .name }}
        {{- else if eq .external false }}
        name: {{ .name}}-{{ $.Values.app }}
        {{- end }}
    {{- end }}
    {{- end }}
    {{- end }}
    {{- if .Values.ConfigSecrets.enabled }}
    {{- range .Values.ConfigSecrets.secrets }}
    {{- if eq .type "environment" }}
    - secretRef:
        {{if eq .external true}}
        name: {{ .name }}
        {{else if eq .external false}}
        name: {{ .name}}-{{ $.Values.app }}
        {{- end }}
    {{- end }}
    {{- end }}
    {{- end }}
    {{- end }}
    resources:
{{ toYaml $.Values.resources | trim | indent 6 }}
    volumeMounts:
{{- with .Values.volumeMounts }}
{{ toYaml . | trim | indent 6 }}
{{- end }}
{{- if  $.Values.persistentVolumeClaim.name }}
      - name: {{ $.Values.persistentVolumeClaim.name }}-vol
        mountPath: {{ $.Values.persistentVolumeClaim.mountPath | default "/tmp" }}
{{- end}}
    {{- if .Values.ConfigMaps.enabled }}
    {{- range .Values.ConfigMaps.maps }}
    {{- if eq .type "volume"}}
    {{- $cmName := .name -}}
    {{- $cmMountPath := .mountPath -}}
    {{- if eq .subPath false }}
      - name: {{ $cmName | replace "." "-"}}-vol
        mountPath: {{ $cmMountPath }}

    {{- else }}
    {{- range $k, $v := .data }}
      - name: {{ $cmName | replace "." "-"}}-vol
        mountPath: {{ $cmMountPath }}/{{ $k}}
        subPath: {{ $k}}
    {{- end }}
    {{- end }}
    {{- end }}
    {{- end }}
    {{- end }}

    {{- if .Values.ConfigSecrets.enabled }}
    {{- range .Values.ConfigSecrets.secrets }}
    {{- if eq .type "volume"}}
    {{- $cmName := .name -}}
    {{- $cmMountPath := .mountPath -}}
    {{- if eq .subPath false }}
      - name: {{ $cmName | replace "." "-"}}-vol
        mountPath: {{ $cmMountPath }}
  
    {{- else }}
    {{if (or (eq .externalType "ESO_GoogleSecretsManager") (eq .externalType "ESO_AWSSecretsManager") (eq .externalType "ESO_HashiCorpVault") (eq .externalType "ESO_AzureSecretsManager"))}}
    {{- if and (.esoSubPath) (ne (len .esoSubPath) 0) }}
    {{- range .esoSubPath }}
      - name: {{ $cmName | replace "." "-"}}-vol
        mountPath: {{ $cmMountPath}}/{{ . }}
        subPath: {{ . }}  
    {{- end }}
    {{- else }}
    {{- range .esoSecretData.esoData }}
      - name: {{ $cmName | replace "." "-"}}-vol
        mountPath: {{ $cmMountPath}}/{{ .secretKey }}
        subPath: {{ .secretKey }}  
    {{- end }}
    {{- end }}
    {{- else }}            
    {{- range $k, $v := .data }} # for others secrets the mount path will be .data[i].secretKey
      - name: {{ $cmName | replace "." "-"}}-vol
        mountPath: {{ $cmMountPath}}/{{ $k}}
        subPath: {{ $k}}
    {{- end }}
    {{- end }}          
    {{- end }}
    {{- end }}
    {{- end }}
    {{- end }}
    {{- if and (eq (len .Values.volumes) 0) (eq ($hasPVCExists) false) (or (eq (.Values.ConfigSecrets.enabled) true) (eq (.Values.ConfigMaps.enabled) true)) (eq ($hasCMVolumeExists) false) (eq ($hasSecretVolumeExists) false) }} []{{- end }}
    {{- if and (eq (len .Values.volumeMounts) 0) (eq ($hasPVCExists) false) (eq (.Values.ConfigSecrets.enabled) false) (eq (.Values.ConfigMaps.enabled) false) }} [] {{- end }}
volumes:
{{- if $.Values.appMetrics }}
  - name: envoy-config-volume
    configMap:
      name: sidecar-config-{{ template ".Chart.Name .name" $ }}
{{- end }}
{{- with .Values.volumes }}
{{ toYaml . | trim | indent 2 }}
{{- end }}
{{- if .Values.ConfigMaps.enabled }}
{{- range .Values.ConfigMaps.maps }}
{{- if eq .type "volume"}}
  - name: {{ .name | replace "." "-"}}-vol
    configMap:
      {{- if eq .external true }}
      name: {{ .name }}
      {{- else if eq .external false }}
      name: {{ .name}}-{{ $.Values.app }}
      {{- end }}
      {{- if eq (len .filePermission) 0 }}
      {{- else }}
      defaultMode: {{ .filePermission}}
      {{- end }}
{{- end }}
{{- end }}
{{- end }}

{{- if .Values.ConfigSecrets.enabled }}
{{- range .Values.ConfigSecrets.secrets }}
{{- if eq .type "volume"}}
  - name: {{ .name | replace "." "-"}}-vol
    secret:
      {{- if eq .external true }}
      secretName: {{ .name }}
      {{- else if eq .external false }}
      secretName: {{ .name}}-{{ $.Values.app }}
      {{- end }}
      {{- if eq (len .filePermission) 0 }}
      {{- else }}
      defaultMode: {{ .filePermission}}
      {{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- if and (eq (len .Values.volumes) 0) (or (eq (.Values.ConfigSecrets.enabled) true) (eq (.Values.ConfigMaps.enabled) true)) (eq ($hasCMVolumeExists) false) (eq ($hasSecretVolumeExists) false) (eq (.Values.appMetrics) false) }} []{{- end }}
{{- if and (eq (len .Values.volumes) 0) (eq (.Values.ConfigSecrets.enabled) false) (eq (.Values.ConfigMaps.enabled) false) (eq (.Values.appMetrics) false) }} []{{- end }}
{{- end }}