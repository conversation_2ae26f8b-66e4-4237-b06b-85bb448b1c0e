{{- if .Values.flaggerCanary.enabled }}
{{ if .Values.flaggerCanary.createIstioGateway.enabled -}}
{{- with .Values.flaggerCanary.createIstioGateway }}
apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: {{ template ".Chart.Name .fullname" $ }}-istio-gateway
  labels:
    app: {{ template ".Chart.Name .name" $ }}
    appId: {{ $.Values.app | quote }}
    envId: {{ $.Values.env | quote }}
    chart: {{ template ".Chart.Name .chart" $ }}
    release: {{ $.Release.Name }}
{{- if $.Values.appLabels }}
{{ toYaml $.Values.appLabels | indent 4 }}
{{- end }}
    {{- if .labels }}
{{ toYaml .labels | indent 4 }}
    {{- end }}
{{- if .annotations }}
  annotations:
{{ toYaml .annotations | indent 4 }}
{{- end }}
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts: 
      - {{ .host | quote -}}
{{- if .tls.enabled }}
    tls:
      httpsRedirect: true
  - port:
      number: 443
      name: https
      protocol: HTTPS
    hosts:
      - {{ .host | quote }}
    tls:
      mode: SIMPLE
      credentialName: {{ .tls.secretName }}  
{{ end }}
{{ end }}
{{ end }}
{{ end }}
---
{{- if .Values.flaggerCanary.enabled }}
{{- with .Values.flaggerCanary }}
apiVersion: flagger.app/v1beta1
kind: Canary
metadata:
  name: {{ template ".Chart.Name .fullname" $ }}-canary
  labels:
    app: {{ template ".Chart.Name .name" $ }}
    appId: {{ $.Values.app | quote }}
    envId: {{ $.Values.env | quote }}
    chart: {{ template ".Chart.Name .chart" $ }}
    release: {{ $.Release.Name }}
{{- if $.Values.appLabels }}
{{ toYaml $.Values.appLabels | indent 4 }}
{{- end }}
  {{- if .labels }}
{{ toYaml .labels | indent 4 }}
  {{- end }}
{{- if .annotations }}
  annotations:
{{ toYaml .annotations | indent 4 }}
{{- end }}
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ include ".Chart.Name .fullname" $ }}
{{- if $.Values.autoscaling.enabled }}
  autoscalerRef:
    apiVersion: autoscaling/v1
    kind: HorizontalPodAutoscaler
    name: {{ template ".Chart.Name .fullname" $ }}-hpa
{{- end }}    
  service:
    portDiscovery: {{ .portDiscovery }}
    port: {{ .serviceport }}
    targetPort: {{ .targetPort }}
    {{- if .appProtocol }}
    appProtocol: {{ .appProtocol }}
    {{- end }}
{{- if $.Values.flaggerCanary.gatewayRefs }}
    gatewayRefs:
{{ toYaml $.Values.flaggerCanary.gatewayRefs | indent 6 }}
{{- end }}
    {{- if or .createIstioGateway.enabled .addOtherGateways }}
    gateways:
     {{- if .createIstioGateway.enabled }}
    - {{ template ".Chart.Name .fullname" $ }}-istio-gateway
     {{- end }}
     {{- if .addOtherGateways }}
     {{- range .addOtherGateways }}
    - {{ .  }}
    {{- end }}
    {{- end }}
    {{- end }}
    {{- if or .createIstioGateway.enabled .addOtherHosts }}
    hosts:
    {{- if .createIstioGateway.enabled }}
    -  {{ .createIstioGateway.host | quote }}
    {{- end }}
    {{- if .addOtherHosts }}
    {{- range .addOtherHosts }}
    - {{ . | quote }}
    {{- end }}
    {{- end }}
    {{- end }}
    {{- if .retries }}
    retries:
{{ toYaml .retries | indent 6 }}  
    {{- end }}
    {{- if .match }}
    match:
    {{- range .match }}
      - uri:
          prefix: {{ .uri.prefix  }}
    {{- end }}
    {{- end }}
    {{- if .rewriteUri }}
    rewrite:
      uri: {{ .rewriteUri }}
    {{- end }}
    {{- if .timeout }}
    timeout: {{ .timeout }}
    {{- end }} 
{{- if $.Values.flaggerCanary.headers }}
    headers:
{{ toYaml $.Values.flaggerCanary.headers | indent 6 }}
{{- end }}
{{- if $.Values.flaggerCanary.corsPolicy }}
    corsPolicy:
{{ toYaml $.Values.flaggerCanary.corsPolicy | indent 6 }}
{{- end }}
  analysis:
    interval: {{ .analysis.interval }}
    threshold: {{ .analysis.threshold }}
    maxWeight: {{ .analysis.maxWeight }}
    stepWeight: {{ .analysis.stepWeight }}
    metrics:
    - name: request-success-rate
      threshold: {{ .thresholds.successRate }}
      interval: 1m
    - name: request-duration
      threshold: {{ .thresholds.latency }}
      interval: 1m
    webhooks:
      {{- if .loadtest.enabled }}
      - name: load-test
        url: {{ .loadtest.url }}
        timeout: 5s
        metadata:
          cmd: "hey -z 1m -q 10 -c 2 http://{{ include ".Chart.Name .fullname" $ }}-canary.{{ $.Release.Namespace }}:{{ $.Values.flaggerCanary.targetPort }}/"
      {{- end }}
{{- end }}
{{- end }}
