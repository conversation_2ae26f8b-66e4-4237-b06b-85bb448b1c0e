{{- if $.Values.serviceAccount }}
{{- if $.Values.serviceAccount.create }}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "serviceAccountName" . }}
  {{- if .Values.podLabels }}
  labels:
{{ toYaml .Values.podLabels | indent 4 }}
  {{- end }}
  {{- if .Values.serviceAccount.annotations }}
  annotations:
{{ toYaml .Values.serviceAccount.annotations | indent 4 }}
  {{- end }}
{{- end -}}
{{- end -}}
