{{- if .Values.winterSoldier.enabled  }}
apiVersion: {{ $.Values.winterSoldier.apiVersion }}
kind: Hibernator
metadata:
  {{- if .Values.winterSoldier.name }}
  name: {{ .Values.winterSoldier.name }}
  {{- else }}
  name: {{ template ".Chart.Name .fullname" $ }}-hibernator
  {{- end }}
  labels:
    app: {{ template ".Chart.Name .name" $ }}
    appId: {{ $.Values.app | quote }}
    envId: {{ $.Values.env | quote }}
    chart: {{ template ".Chart.Name .chart" $ }}
    release: {{ $.Release.Name }}
{{- if .Values.appLabels }}
{{ toYaml .Values.appLabels | indent 4 }}
{{- end }}
    {{- if .Values.winterSoldier.labels }}
{{ toYaml .Values.winterSoldier.labels | indent 4 }}
    {{- end }}
{{- if .Values.winterSoldier.annotations }}
  annotations:
{{ toYaml .Values.winterSoldier.annotations | indent 4 }}
{{- end }}
spec:
  timeRangesWithZone:
{{ toYaml $.Values.winterSoldier.timeRangesWithZone | indent 4}}
  selectors:
    - inclusions:
        - objectSelector:
            name: {{ include ".Chart.Name .fullname" $ }}
            type: {{ .Values.winterSoldier.type | quote }}
            fieldSelector:
{{toYaml $.Values.winterSoldier.fieldSelector | indent 14 }} 
          namespaceSelector:
            name: {{ $.Release.Namespace }}
      exclusions: []
  action: {{ $.Values.winterSoldier.action }}
  {{- if eq .Values.winterSoldier.action "scale" }}
  {{- if  .Values.winterSoldier.targetReplicas }}
  targetReplicas: {{ $.Values.winterSoldier.targetReplicas }}
  {{- end }}
  {{- end }}
{{- end }}
