{"name": "test-1", "stages": [{"name": "defaultStage", "fetch_materials": true, "clean_working_directory": false, "never_cleanup_artifacts": false, "approval": {"type": "success", "authorization": {"roles": [], "users": []}}, "environment_variables": [], "jobs": [{"name": "defaultJob", "run_instance_count": null, "timeout": null, "elastic_profile_id": "helm", "environment_variables": [], "resources": [], "tasks": [{"type": "fetch", "attributes": {"artifact_origin": "external", "pipeline": "{{.upstreamPipeline.name}}", "stage": "{{.upstreamPipeline.stage}}", "job": "{{.upstreamPipeline.job}}", "run_if": ["passed"], "artifact_id": "{{.upstreamPipeline.artifactId}}", "configuration": [{"key": "EnvironmentVariablePrefix"}, {"key": "SkipImagePulling", "value": "true"}]}}, {"type": "pluggable_task", "attributes": {"run_if": ["passed"], "plugin_configuration": {"id": "ai.devtron.task.fetch-app-config", "version": "1"}, "configuration": [{"key": "command", "value": "BG_DEPLOY"}]}}, {"type": "exec", "attributes": {"run_if": ["passed"], "command": "bash", "arguments": ["-c", "sh helm-init.sh"]}}, {"type": "exec", "attributes": {"run_if": ["passed"], "command": "bash", "arguments": ["-c", "sh command.sh"]}}, {"type": "pluggable_task", "attributes": {"run_if": ["failed"], "plugin_configuration": {"id": "ai.devtron.task.fetch-app-config", "version": "1"}, "configuration": [{"key": "command", "value": "BG_ERROR"}]}}, {"type": "pluggable_task", "attributes": {"run_if": ["passed"], "plugin_configuration": {"id": "ai.devtron.task.fetch-app-config", "version": "1"}, "configuration": [{"key": "command", "value": "BG_TOGGLE"}]}}, {"type": "exec", "attributes": {"run_if": ["passed"], "command": "bash", "arguments": ["-c", "sh command.sh"]}}], "tabs": [], "artifacts": [], "properties": null}]}]}