# Table of contents

* [Introduction](README.md)
* [Getting Started](setup/getting-started/getting-started.md)
* [Install Devtron](setup/install/README.md)
  * [Install Devtron with CI/CD](setup/install/install-devtron-with-cicd.md)
  * [Install Devtron with CI/CD and GitOps (Argo CD)](setup/install/install-devtron-with-cicd-with-gitops.md)
  * [Install Devtron without Integrations](setup/install/install-devtron.md)
  * [Install Devtron on Minikube, Microk8s, K3s, Kind, Cloud VMs](setup/install/Install-devtron-on-Minikube-Microk8s-K3s-Kind.md)
  * [Install Devtron on Airgapped Environment](setup/install/install-devtron-in-airgapped-environment.md)
  * [Demo on Popular Cloud Providers](setup/install/demo-tutorials.md)
  * [Backup for Disaster Recovery](setup/install/devtron-backup.md)
  * [Uninstall Devtron](setup/install/uninstall-devtron.md)
  * [FAQs](setup/install/faq-on-installation.md)
* [Devtron Kubernetes Client](setup/install/install-devtron-Kubernetes-client.md)
* [Configurations](setup/configurations/configurations-overview.md)
  * [Installation Configurations](setup/install/installation-configuration.md)
  * [Override Configurations](setup/install/override-default-devtron-installation-configs.md)
  * [Ingress Setup](setup/install/ingress-setup.md)
* [Global Configurations](user-guide/global-configurations/README.md)
  * [Host URL](user-guide/global-configurations/host-url.md)
  * [GitOps](user-guide/global-configurations/gitops.md)
  * [Projects](user-guide/global-configurations/projects.md)
  * [Clusters & Environments](user-guide/global-configurations/cluster-and-environments.md)
  * [Git Accounts](user-guide/global-configurations/git-accounts.md)
  * [Container/OCI Registry](user-guide/global-configurations/container-registries.md)
  * [Chart Repositories](user-guide/global-configurations/chart-repo.md)
  * [Deployment Charts](user-guide/global-configurations/deployment-charts.md)
  * [Authorization](user-guide/global-configurations/authorization/README.md)
    * [SSO Login Services](user-guide/global-configurations/sso-login.md)
      * [Google](user-guide/global-configurations/authorization/sso/google.md)
      * [GitHub](user-guide/global-configurations/authorization/sso/github.md)
      * [GitLab](user-guide/global-configurations/authorization/sso/gitlab.md)
      * [Microsoft](user-guide/global-configurations/authorization/sso/microsoft.md)
      * [LDAP](user-guide/global-configurations/authorization/sso/ldap.md)
      * [OIDC](user-guide/global-configurations/authorization/sso/oidc.md)
        * [Keycloak](user-guide/global-configurations/authorization/sso/keycloak.md)
        * [Okta](user-guide/global-configurations/authorization/sso/okta.md)
      * [OpenShift](user-guide/global-configurations/authorization/sso/openshift.md)
    * [User Permissions](user-guide/global-configurations/authorization/user-access.md)
    * [Permission Groups](user-guide/global-configurations/authorization/permission-groups.md)
    * [API Tokens](user-guide/global-configurations/authorization/api-tokens.md)
  * [Notifications](user-guide/global-configurations/manage-notification.md)
  * [Deployment Window](user-guide/global-configurations/deployment-window.md)
  * [External Links](user-guide/global-configurations/external-links.md)
  * [Catalog Framework](user-guide/global-configurations/catalog-framework.md)
  * [Scoped Variables](user-guide/global-configurations/scoped-variables.md)
  * [Pull Image Digest](user-guide/global-configurations/pull-image-digest.md)
  * [Tags Policy](user-guide/global-configurations/tags-policy.md)
  * [Filter Condition](user-guide/global-configurations/filter-condition.md)
  * [Lock Deployment Configuration](user-guide/global-configurations/lock-deployment-config.md)
  * [Image Promotion Policy](user-guide/global-configurations/image-promotion-policy.md)  
  * [Build Infra](user-guide/global-configurations/build-infra.md)
* [Devtron Upgrade](setup/upgrade/README.md)
  * [Update Devtron from Devtron UI](setup/upgrade/upgrade-devtron-ui.md)
  * [0.6.x-0.7.x](setup/upgrade/devtron-upgrade-0.6.x-0.7.x.md)
  * [0.5.x-0.6.x](setup/upgrade/devtron-upgrade-0.5.x-0.6.x.md)
  * [0.4.x-0.5.x](setup/upgrade/devtron-upgrade-0.4.x-0.5.x.md)
  * [0.4.x-0.4.x](setup/upgrade/devtron-upgrade-0.4.x-0.4.x.md)
  * [0.3.x-0.4.x](setup/upgrade/devtron-upgrade-0.3.x-0.4.x.md)
  * [0.3.x-0.3.x](setup/upgrade/devtron-upgrade-0.3.x-0.3.x.md)
  * [0.2.x-0.3.x](setup/upgrade/devtron-upgrade-0.2.x-0.3.x.md)

## Usage

* [Applications](user-guide/applications.md)
  * [Create a New Application](user-guide/create-application.md)
  * [Clone an Existing Application](user-guide/cloning-application.md)
  * [Deploy a Sample Application](user-guide/Deploy-sample-app/nodejs\_app.md)
  * [App Configuration](user-guide/creating-application/README.md)
    * [Git Repository](user-guide/creating-application/git-material.md)
    * [Build Configuration](user-guide/creating-application/docker-build-configuration.md)
    * [Base Deployment Template](user-guide/creating-application/deployment-template.md)
      * [Deployment](user-guide/creating-application/deployment-template/deployment.md)
      * [Rollout Deployment](user-guide/creating-application/deployment-template/rollout-deployment.md)
      * [Job and Cronjob](user-guide/creating-application/deployment-template/job-and-cronjob.md)
      * [StatefulSets](user-guide/creating-application/deployment-template/statefulset.md)
    * [GitOps Configuration](user-guide/creating-application/gitops-config.md)
    * [Workflow Editor](user-guide/creating-application/workflow/README.md)
      * [CI Pipeline](user-guide/creating-application/workflow/ci-pipeline.md)
        * [Pre-Build/Post-Build Stages](user-guide/creating-application/workflow/ci-build-pre-post-plugins.md)
        * [Override Build Configuration](user-guide/creating-application/container-registry-override.md)
      * [CD Pipeline](user-guide/creating-application/workflow/cd-pipeline.md)
    * [ConfigMaps](user-guide/creating-application/config-maps.md)
    * [Secrets](user-guide/creating-application/secrets.md)
      * [External Secret Operator (ESO)](user-guide/creating-application/eso/README.md)
        * [AWS Secrets Manager](user-guide/creating-application/eso/aws-eso.md)
        * [Google Secrets Manager](user-guide/creating-application/eso/gcp-eso.md)
        * [HashiCorp Vault](user-guide/creating-application/eso/hashicorp-eso.md)
    * [Protect Configuration](user-guide/creating-application/config-approval.md)
    * [Environment Overrides](user-guide/creating-application/environment-overrides.md)
    * [Deleting Application](user-guide/deleting-application.md)
  * [Build and Deploy](user-guide/deploying-application/README.md)
    * [Triggering CI](user-guide/deploying-application/triggering-ci.md)
    * [Triggering CD](user-guide/deploying-application/triggering-cd.md)
    * [Rollback Deployment](user-guide/deploying-application/rollback-deployment.md)
    * [Applying Labels to Images](user-guide/deploying-application/image-labels-and-comments.md)
  * [App Details](user-guide/creating-application/app-details.md)
    * [Debugging Deployment And Monitoring](user-guide/debugging-deployment-and-monitoring.md)
    * [Using Ephemeral Containers](user-guide/app-details/ephemeral-containers.md)
    * [Application Metrics](user-guide/creating-application/app-metrics.md)
  * [Overview](user-guide/creating-application/overview.md)
* [Jobs](user-guide/jobs/README.md)
  * [Create a new job](user-guide/jobs/create-job.md)
  * [Configurations](user-guide/jobs/configuration-job.md)
  * [Workflow Editor](user-guide/jobs/workflow-editor-job.md)
  * [Trigger Job](user-guide/jobs/triggering-job.md)
  * [Overview](user-guide/jobs/overview-job.md) 
* [Application Groups](user-guide/application-groups.md)
* [Software Distribution Hub](user-guide/sdh/README.md)
  * [Tenants](user-guide/sdh/tenants.md)
  * [Release Hub](user-guide/sdh/release-hub.md)
* [Resource Browser](user-guide/resource-browser.md)   
* [Resource Watcher](user-guide/resource-watcher.md) 
* [Charts](user-guide/deploy-chart/README.md)
  * [Charts Overview](user-guide/deploy-chart/overview-of-charts.md)
  * [Deploy & Observe](user-guide/deploy-chart/deployment-of-charts.md)
  * [Examples](user-guide/deploy-chart/examples/README.md)
    * [Deploying Mysql Helm Chart](user-guide/deploy-chart/examples/deploying-mysql-helm-chart.md)
    * [Deploying MongoDB Helm Chart](user-guide/deploy-chart/examples/deploying-mongodb-helm-chart.md)
  * [Chart Group](user-guide/deploy-chart/chart-group.md)
* [Security](user-guide/security-features.md)
  * [Security Scans](user-guide/security-features/security-scans.md)
  * [Security Policies](user-guide/security-features/security-policies.md)
* [Bulk Edit](user-guide/bulk-update.md)
* [Integrations](user-guide/integrations/README.md)
  * [Build and Deploy (CI/CD)](user-guide/integrations/build-and-deploy-ci-cd.md)
  * [GitOps (Argo CD)](user-guide/integrations/argocd.md)
  * [Vulnerability Scanning (Clair)](user-guide/integrations/clair.md)
  * [Notifications](user-guide/integrations/notifications.md)
  * [Monitoring (Grafana)](user-guide/integrations/grafana.md)
* [Pipeline Plugins](user-guide/plugins/README.md)
  * [Create Your Plugin](user-guide/plugins/create-plugin.md)
  * [Our Plugins](user-guide/plugins/plugin-list.md)
    * [Codacy](user-guide/plugins/codacy.md)
    * [Code-Scan](user-guide/plugins/code-scan.md)
    * [Copacetic](user-guide/plugins/copacetic.md)
    * [Copy Container Image](user-guide/plugins/copy-container-image.md)
    * [Cosign](user-guide/plugins/cosign.md)
    * [CraneCopy](user-guide/plugins/crane-copy.md)
    * [Dependency track - Maven & Gradle](user-guide/plugins/dependency-track-maven-gradle.md)
    * [Dependency track - NodeJS](user-guide/plugins/dependency-track-nodejs.md)
    * [Dependency track - Python](user-guide/plugins/dependency-track-python.md)
    * [Devtron CD Trigger](user-guide/plugins/devtron-cd-trigger.md)
    * [Devtron Job Trigger](user-guide/plugins/devtron-job-trigger.md)
    * [DockerSlim](user-guide/plugins/docker-slim.md)
    * [GoLang-migrate](user-guide/plugins/golang-migrate.md)
    * [Jenkins](user-guide/plugins/jenkins.md)
    * [Jira Issue Validator](user-guide/plugins/jira-validator.md)
    * [Jira Issue Updater](user-guide/plugins/jira-updater.md)
    * [K6 Load Testing](user-guide/plugins/k6-load-testing.md)
    * [Pull images from container repository](user-guide/plugins/pull-images-from-container-repository.md)
    * [Semgrep](user-guide/plugins/semgrep.md)
    * [SonarQube](user-guide/plugins/sonarqube.md)
    * [SonarQube v1.1.0](user-guide/plugins/sonarqube-v1.1.0.md)
    * [Vulnerability Scanning](user-guide/plugins/vulnerability-scanning.md)


## Resources

* [Glossary](reference/glossary.md)
* [Troubleshooting](FAQs/devtron-troubleshoot.md)
* [Use Cases](user-guide/use-cases/README.md)
  * [Devtron Generic Helm Chart To Run CronJob Or One Time Job](user-guide/use-cases/devtron-generic-helm-chart-to-run-cron-job-or-one-time-job.md)
  * [Connect SpringBoot with Mysql Database](user-guide/use-cases/connect-springboot-with-mysql-database.md)
  * [Connect Expressjs With Mongodb Database](user-guide/use-cases/connect-expressjs-with-mongodb-database.md)
  * [Connect Django With Mysql Database](user-guide/use-cases/connect-django-with-mysql-database.md)
  * [Pull Helm Charts from OCI Registry](user-guide/use-cases/oci-pull.md)
* [Telemetry Overview](user-guide/telemetry.md)
* [Devtron on Graviton](reference/graviton.md)
* [Release Notes](https://github.com/devtron-labs/devtron/releases)
