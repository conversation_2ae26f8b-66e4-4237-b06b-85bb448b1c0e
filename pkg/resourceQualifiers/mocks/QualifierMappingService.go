// Code generated by mockery v2.34.0. DO NOT EDIT.

package mocks

import (
	bean "github.com/devtron-labs/devtron/pkg/devtronResource/bean"
	mock "github.com/stretchr/testify/mock"

	pg "github.com/go-pg/pg"

	resourceQualifiers "github.com/devtron-labs/devtron/pkg/resourceQualifiers"

	sql "github.com/devtron-labs/devtron/pkg/sql"
)

// QualifierMappingService is an autogenerated mock type for the QualifierMappingService type
type QualifierMappingService struct {
	mock.Mock
}

// CreateQualifierMappings provides a mock function with given fields: qualifierMappings, tx
func (_m *QualifierMappingService) CreateQualifierMappings(qualifierMappings []*resourceQualifiers.QualifierMapping, tx *pg.Tx) ([]*resourceQualifiers.QualifierMapping, error) {
	ret := _m.Called(qualifierMappings, tx)

	var r0 []*resourceQualifiers.QualifierMapping
	var r1 error
	if rf, ok := ret.Get(0).(func([]*resourceQualifiers.QualifierMapping, *pg.Tx) ([]*resourceQualifiers.QualifierMapping, error)); ok {
		return rf(qualifierMappings, tx)
	}
	if rf, ok := ret.Get(0).(func([]*resourceQualifiers.QualifierMapping, *pg.Tx) []*resourceQualifiers.QualifierMapping); ok {
		r0 = rf(qualifierMappings, tx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*resourceQualifiers.QualifierMapping)
		}
	}

	if rf, ok := ret.Get(1).(func([]*resourceQualifiers.QualifierMapping, *pg.Tx) error); ok {
		r1 = rf(qualifierMappings, tx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DeleteAllQualifierMappings provides a mock function with given fields: resourceType, auditLog, tx
func (_m *QualifierMappingService) DeleteAllQualifierMappings(resourceType resourceQualifiers.ResourceType, auditLog sql.AuditLog, tx *pg.Tx) error {
	ret := _m.Called(resourceType, auditLog, tx)

	var r0 error
	if rf, ok := ret.Get(0).(func(resourceQualifiers.ResourceType, sql.AuditLog, *pg.Tx) error); ok {
		r0 = rf(resourceType, auditLog, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetQualifierMappings provides a mock function with given fields: resourceType, scope, searchableIdMap, resourceIds
func (_m *QualifierMappingService) GetQualifierMappings(resourceType resourceQualifiers.ResourceType, scope resourceQualifiers.Scope, searchableIdMap map[bean.DevtronResourceSearchableKeyName]int, resourceIds []int) ([]*resourceQualifiers.QualifierMapping, error) {
	ret := _m.Called(resourceType, scope, searchableIdMap, resourceIds)

	var r0 []*resourceQualifiers.QualifierMapping
	var r1 error
	if rf, ok := ret.Get(0).(func(resourceQualifiers.ResourceType, resourceQualifiers.Scope, map[bean.DevtronResourceSearchableKeyName]int, []int) ([]*resourceQualifiers.QualifierMapping, error)); ok {
		return rf(resourceType, scope, searchableIdMap, resourceIds)
	}
	if rf, ok := ret.Get(0).(func(resourceQualifiers.ResourceType, resourceQualifiers.Scope, map[bean.DevtronResourceSearchableKeyName]int, []int) []*resourceQualifiers.QualifierMapping); ok {
		r0 = rf(resourceType, scope, searchableIdMap, resourceIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*resourceQualifiers.QualifierMapping)
		}
	}

	if rf, ok := ret.Get(1).(func(resourceQualifiers.ResourceType, resourceQualifiers.Scope, map[bean.DevtronResourceSearchableKeyName]int, []int) error); ok {
		r1 = rf(resourceType, scope, searchableIdMap, resourceIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewQualifierMappingService creates a new instance of QualifierMappingService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewQualifierMappingService(t interface {
	mock.TestingT
	Cleanup(func())
}) *QualifierMappingService {
	mock := &QualifierMappingService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
