/*
 * Copyright (c) 2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package service

import (
	"github.com/devtron-labs/devtron/internal/sql/repository"
	appStoreDiscoverRepository "github.com/devtron-labs/devtron/pkg/appStore/discover/repository"
	"github.com/devtron-labs/devtron/pkg/attributes"
	"github.com/devtron-labs/devtron/pkg/cluster/environment"
	repository4 "github.com/devtron-labs/devtron/pkg/cluster/environment/repository"
	"github.com/devtron-labs/devtron/pkg/deployment/providerConfig"
	util3 "github.com/devtron-labs/devtron/util"
	"testing"

	util2 "github.com/devtron-labs/common-lib/utils/k8s"
	"github.com/devtron-labs/devtron/internal/sql/repository/app"
	"github.com/devtron-labs/devtron/internal/util"
	appStoreBean "github.com/devtron-labs/devtron/pkg/appStore/bean"
	repository3 "github.com/devtron-labs/devtron/pkg/appStore/installedApp/repository"
	repository5 "github.com/devtron-labs/devtron/pkg/auth/user/repository"
	"github.com/devtron-labs/devtron/pkg/cluster"
	repository2 "github.com/devtron-labs/devtron/pkg/cluster/repository"
	"github.com/devtron-labs/devtron/pkg/sql"
	"github.com/stretchr/testify/assert"
)

func TestAppStoreDeploymentService(t *testing.T) {

	// not considered scenario when gitops is not enabled.

	// testing GitOps vs Helm option in chart store
	t.SkipNow()
	t.Run("AppStoreDeployOperationDBInternalUse", func(t *testing.T) {
		AppStoreDeploymentService := initAppStoreDeploymentService(t, true)

		sugaredLogger, _ := util.InitLogger()
		config, _ := sql.GetConfig()
		db, _ := sql.NewDbConnection(config, sugaredLogger)

		tx, _ := db.Begin()

		InstallAppVersionDTO := appStoreBean.InstallAppVersionDTO{
			Id:                        0,
			AppId:                     0,
			AppName:                   "samplesamplesample",
			TeamId:                    1,
			EnvironmentId:             1,
			InstalledAppId:            0,
			InstalledAppVersionId:     0,
			AppStoreVersion:           6304,
			ValuesOverrideYaml:        "## @section Global parameters\n## Global Docker image parameters\n## Please, note that this will override the image parameters, including dependencies, configured to use the global value\n## Current available global Docker image parameters: imageRegistry, imagePullSecrets and storageClass\n\n## @param global.imageRegistry Global Docker image registry\n## @param global.imagePullSecrets Global Docker registry secret names as an array\n## @param global.storageClass Global StorageClass for Persistent Volume(s)\n##\nglobal:\n  imageRegistry: \"\"\n  ## E.g.\n  ## imagePullSecrets:\n  ##   - myRegistryKeySecretName\n  ##\n  imagePullSecrets: []\n  storageClass: \"\"\n\n## @section Common parameters\n\n## @param kubeVersion Override Kubernetes version\n##\nkubeVersion: \"\"\n## @param nameOverride String to partially override common.names.fullname template (will maintain the release name)\n##\nnameOverride: \"\"\n## @param fullnameOverride String to fully override common.names.fullname template\n##\nfullnameOverride: \"\"\n## @param clusterDomain Kubernetes Cluster Domain\n##\nclusterDomain: cluster.local\n## @param extraDeploy Extra objects to deploy (evaluated as a template)\n##\nextraDeploy: []\n## @param commonLabels Add labels to all the deployed resources\n##\ncommonLabels: {}\n## @param commonAnnotations Add annotations to all the deployed resources\n##\ncommonAnnotations: {}\n## Enable diagnostic mode in the deployment(s)/statefulset(s)\n##\ndiagnosticMode:\n  ## @param diagnosticMode.enabled Enable diagnostic mode (all probes will be disabled and the command will be overridden)\n  ##\n  enabled: false\n  ## @param diagnosticMode.command Command to override all containers in the the deployment(s)/statefulset(s)\n  ##\n  command:\n    - sleep\n  ## @param diagnosticMode.args Args to override all containers in the the deployment(s)/statefulset(s)\n  ##\n  args:\n    - infinity\n\n## @section Airflow common parameters\n\n## Authentication parameters\n## ref: https://github.com/bitnami/containers/tree/main/bitnami/airflow#environment-variables\n##\nauth:\n  ## @param auth.username Username to access web UI\n  ##\n  username: user\n  ## @param auth.password Password to access web UI\n  ##\n  password: \"\"\n  ## @param auth.fernetKey Fernet key to secure connections\n  ## ref: https://airflow.readthedocs.io/en/stable/howto/secure-connections.html\n  ## ref: https://bcb.github.io/airflow/fernet-key\n  ##\n  fernetKey: \"\"\n  ## @param auth.secretKey Secret key to run your flask app\n  ## ref: https://airflow.apache.org/docs/apache-airflow/stable/configurations-ref.html#secret-key\n  ##\n  secretKey: \"\"\n  ## @param auth.existingSecret Name of an existing secret to use for Airflow credentials\n  ## `auth.password`, `auth.fernetKey`, and `auth.secretKey` will be ignored and picked up from this secret\n  ## The secret must contain the keys `airflow-password`, `airflow-fernet-key` and `airflow-secret-key'\n  ## The value is evaluated as a template\n  ##\n  existingSecret: \"\"\n## @param executor Airflow executor. Allowed values: `SequentialExecutor`, `LocalExecutor`, `CeleryExecutor`, `KubernetesExecutor`, `CeleryKubernetesExecutor` and `LocalKubernetesExecutor`\n## ref: http://airflow.apache.org/docs/stable/executor/index.html\n##\nexecutor: CeleryExecutor\n## @param loadExamples Switch to load some Airflow examples\n##\nloadExamples: false\n## @param configuration Specify content for Airflow config file (auto-generated based on other env. vars otherwise)\n## e.g:\n## configuration: |-\n##   [core]\n##   dags_folder=/opt/bitnami/airflow/dags\n##   ...\n##\nconfiguration: \"\"\n## @param existingConfigmap Name of an existing ConfigMap with the Airflow config file\n##\nexistingConfigmap: \"\"\n## Load custom DAGs from a ConfigMap\n## Note: an init container will be used to prepare the DAGs available in the ConfigMap to be consumed by Airflow containers\n##\ndags:\n  ## @param dags.existingConfigmap Name of an existing ConfigMap with all the DAGs files you want to load in Airflow\n  ##\n  existingConfigmap: \"\"\n  ## Bitnami Shell image\n  ## ref: https://hub.docker.com/r/bitnami/bitnami-shell/tags/\n  ## @param dags.image.registry Init container load-dags image registry\n  ## @param dags.image.repository Init container load-dags image repository\n  ## @param dags.image.tag Init container load-dags image tag (immutable tags are recommended)\n  ## @param dags.image.digest Init container load-dags image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag\n  ## @param dags.image.pullPolicy Init container load-dags image pull policy\n  ## @param dags.image.pullSecrets Init container load-dags image pull secrets\n  ##\n  image:\n    registry: docker.io\n    repository: bitnami/bitnami-shell\n    tag: 11-debian-11-r50\n    digest: \"\"\n    pullPolicy: IfNotPresent\n    ## Optionally specify an array of imagePullSecrets.\n    ## Secrets must be manually created in the namespace.\n    ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/\n    ## e.g:\n    ## pullSecrets:\n    ##   - myRegistryKeySecretName\n    ##\n    pullSecrets: []\n## @param extraEnvVars Add extra environment variables for all the Airflow pods\n##\nextraEnvVars: []\n## @param extraEnvVarsCM ConfigMap with extra environment variables for all the Airflow pods\n##\nextraEnvVarsCM: \"\"\n## @param extraEnvVarsSecret Secret with extra environment variables for all the Airflow pods\n##\nextraEnvVarsSecret: \"\"\n## @param extraEnvVarsSecrets List of secrets with extra environment variables for all the Airflow pods\n##\nextraEnvVarsSecrets: []\n## @param sidecars Add additional sidecar containers to all the Airflow pods\n## Example:\n## sidecars:\n##   - name: your-image-name\n##     image: your-image\n##     imagePullPolicy: Always\n##     ports:\n##       - name: portname\n##         containerPort: 1234\n##\nsidecars: []\n## @param initContainers Add additional init containers to all the Airflow pods\n## Example:\n## initContainers:\n##   - name: your-image-name\n##     image: your-image\n##     imagePullPolicy: Always\n##     ports:\n##       - name: portname\n##         containerPort: 1234\n##\ninitContainers: []\n## @param extraVolumeMounts Optionally specify extra list of additional volumeMounts for all the Airflow pods\n##\nextraVolumeMounts: []\n## @param extraVolumes Optionally specify extra list of additional volumes for the all the Airflow pods\n##\nextraVolumes: []\n\n## @section Airflow web parameters\n\nweb:\n  ## Bitnami Airflow image version\n  ## ref: https://hub.docker.com/r/bitnami/airflow/tags/\n  ## @param web.image.registry Airflow image registry\n  ## @param web.image.repository Airflow image repository\n  ## @param web.image.tag Airflow image tag (immutable tags are recommended)\n  ## @param web.image.digest Airflow image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag\n  ## @param web.image.pullPolicy Airflow image pull policy\n  ## @param web.image.pullSecrets Airflow image pull secrets\n  ## @param web.image.debug Enable image debug mode\n  image:\n    registry: docker.io\n    repository: bitnami/airflow\n    tag: 2.4.2-debian-11-r6\n    digest: \"\"\n    ## Specify a imagePullPolicy\n    ## Defaults to 'Always' if image tag is 'latest', else set to 'IfNotPresent'\n    ## ref: https://kubernetes.io/docs/user-guide/images/#pre-pulling-images\n    ##\n    pullPolicy: IfNotPresent\n    ## Optionally specify an array of imagePullSecrets.\n    ## Secrets must be manually created in the namespace.\n    ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/\n    ## e.g:\n    ## pullSecrets:\n    ##   - myRegistryKeySecretName\n    ##\n    pullSecrets: []\n    ## Set to true if you would like to see extra information on logs\n    ##\n    debug: false\n  ## @param web.baseUrl URL used to access to Airflow web ui\n  ##\n  baseUrl: \"\"\n  ## @param web.existingConfigmap Name of an existing config map containing the Airflow web config file\n  ##\n  existingConfigmap: \"\"\n  ## @param web.command Override default container command (useful when using custom images)\n  ##\n  command: []\n  ## @param web.args Override default container args (useful when using custom images)\n  ##\n  args: []\n  ## @param web.extraEnvVars Array with extra environment variables to add Airflow web pods\n  ##\n  extraEnvVars: []\n  ## @param web.extraEnvVarsCM ConfigMap containing extra environment variables for Airflow web pods\n  ##\n  extraEnvVarsCM: \"\"\n  ## @param web.extraEnvVarsSecret Secret containing extra environment variables (in case of sensitive data) for Airflow web pods\n  ##\n  extraEnvVarsSecret: \"\"\n  ## @param web.extraEnvVarsSecrets List of secrets with extra environment variables for Airflow web pods\n  ##\n  extraEnvVarsSecrets: []\n  ## @param web.containerPorts.http Airflow web HTTP container port\n  ##\n  containerPorts:\n    http: 8080\n  ## @param web.replicaCount Number of Airflow web replicas\n  ##\n  replicaCount: 1\n  ## Configure extra options for Airflow web containers' liveness, readiness and startup probes\n  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/#configure-probes\n  ## @param web.livenessProbe.enabled Enable livenessProbe on Airflow web containers\n  ## @param web.livenessProbe.initialDelaySeconds Initial delay seconds for livenessProbe\n  ## @param web.livenessProbe.periodSeconds Period seconds for livenessProbe\n  ## @param web.livenessProbe.timeoutSeconds Timeout seconds for livenessProbe\n  ## @param web.livenessProbe.failureThreshold Failure threshold for livenessProbe\n  ## @param web.livenessProbe.successThreshold Success threshold for livenessProbe\n  ##\n  livenessProbe:\n    enabled: true\n    initialDelaySeconds: 180\n    periodSeconds: 20\n    timeoutSeconds: 5\n    failureThreshold: 6\n    successThreshold: 1\n  ## @param web.readinessProbe.enabled Enable readinessProbe on Airflow web containers\n  ## @param web.readinessProbe.initialDelaySeconds Initial delay seconds for readinessProbe\n  ## @param web.readinessProbe.periodSeconds Period seconds for readinessProbe\n  ## @param web.readinessProbe.timeoutSeconds Timeout seconds for readinessProbe\n  ## @param web.readinessProbe.failureThreshold Failure threshold for readinessProbe\n  ## @param web.readinessProbe.successThreshold Success threshold for readinessProbe\n  ##\n  readinessProbe:\n    enabled: true\n    initialDelaySeconds: 30\n    periodSeconds: 10\n    timeoutSeconds: 5\n    failureThreshold: 6\n    successThreshold: 1\n  ## @param web.startupProbe.enabled Enable startupProbe on Airflow web containers\n  ## @param web.startupProbe.initialDelaySeconds Initial delay seconds for startupProbe\n  ## @param web.startupProbe.periodSeconds Period seconds for startupProbe\n  ## @param web.startupProbe.timeoutSeconds Timeout seconds for startupProbe\n  ## @param web.startupProbe.failureThreshold Failure threshold for startupProbe\n  ## @param web.startupProbe.successThreshold Success threshold for startupProbe\n  ##\n  startupProbe:\n    enabled: false\n    initialDelaySeconds: 60\n    periodSeconds: 10\n    timeoutSeconds: 1\n    failureThreshold: 15\n    successThreshold: 1\n  ## @param web.customLivenessProbe Custom livenessProbe that overrides the default one\n  ##\n  customLivenessProbe: {}\n  ## @param web.customReadinessProbe Custom readinessProbe that overrides the default one\n  ##\n  customReadinessProbe: {}\n  ## @param web.customStartupProbe Custom startupProbe that overrides the default one\n  ##\n  customStartupProbe: {}\n  ## Airflow web resource requests and limits\n  ## ref: https://kubernetes.io/docs/user-guide/compute-resources/\n  ## @param web.resources.limits The resources limits for the Airflow web containers\n  ## @param web.resources.requests The requested resources for the Airflow web containers\n  ##\n  resources:\n    limits: {}\n    requests: {}\n  ## Configure Airflow web pods Security Context\n  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod\n  ## @param web.podSecurityContext.enabled Enabled Airflow web pods' Security Context\n  ## @param web.podSecurityContext.fsGroup Set Airflow web pod's Security Context fsGroup\n  ##\n  podSecurityContext:\n    enabled: true\n    fsGroup: 1001\n  ## Configure Airflow web containers (only main one) Security Context\n  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container\n  ## @param web.containerSecurityContext.enabled Enabled Airflow web containers' Security Context\n  ## @param web.containerSecurityContext.runAsUser Set Airflow web containers' Security Context runAsUser\n  ## @param web.containerSecurityContext.runAsNonRoot Set Airflow web containers' Security Context runAsNonRoot\n  ##\n  containerSecurityContext:\n    enabled: true\n    runAsUser: 1001\n    runAsNonRoot: true\n  ## @param web.lifecycleHooks for the Airflow web container(s) to automate configuration before or after startup\n  ##\n  lifecycleHooks: {}\n  ## @param web.hostAliases Deployment pod host aliases\n  ## https://kubernetes.io/docs/concepts/services-networking/add-entries-to-pod-etc-hosts-with-host-aliases/\n  ##\n  hostAliases: []\n  ## @param web.podLabels Add extra labels to the Airflow web pods\n  ##\n  podLabels: {}\n  ## @param web.podAnnotations Add extra annotations to the Airflow web pods\n  ##\n  podAnnotations: {}\n  ## @param web.affinity Affinity for Airflow web pods assignment (evaluated as a template)\n  ## Ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity\n  ## Note: `web.podAffinityPreset`, `web.podAntiAffinityPreset`, and `web.nodeAffinityPreset` will be ignored when it's set\n  ##\n  affinity: {}\n  ## Node affinity preset\n  ## Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#node-affinity\n  ## @param web.nodeAffinityPreset.key Node label key to match. Ignored if `web.affinity` is set.\n  ## @param web.nodeAffinityPreset.type Node affinity preset type. Ignored if `web.affinity` is set. Allowed values: `soft` or `hard`\n  ## @param web.nodeAffinityPreset.values Node label values to match. Ignored if `web.affinity` is set.\n  ##\n  nodeAffinityPreset:\n    ## e.g:\n    ## key: \"kubernetes.io/e2e-az-name\"\n    ##\n    key: \"\"\n    type: \"\"\n    ## e.g:\n    ## values:\n    ##   - e2e-az1\n    ##   - e2e-az2\n    ##\n    values: []\n  ## @param web.nodeSelector Node labels for Airflow web pods assignment\n  ## Ref: https://kubernetes.io/docs/user-guide/node-selection/\n  ##\n  nodeSelector: {}\n  ## @param web.podAffinityPreset Pod affinity preset. Ignored if `web.affinity` is set. Allowed values: `soft` or `hard`.\n  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity\n  ##\n  podAffinityPreset: \"\"\n  ## @param web.podAntiAffinityPreset Pod anti-affinity preset. Ignored if `web.affinity` is set. Allowed values: `soft` or `hard`.\n  ## Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity\n  ##\n  podAntiAffinityPreset: soft\n  ## @param web.tolerations Tolerations for Airflow web pods assignment\n  ## Ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/\n  ##\n  tolerations: []\n  ## @param web.topologySpreadConstraints Topology Spread Constraints for pod assignment spread across your cluster among failure-domains. Evaluated as a template\n  ## Ref: https://kubernetes.io/docs/concepts/workloads/pods/pod-topology-spread-constraints/#spread-constraints-for-pods\n  ##\n  topologySpreadConstraints: []\n  ## @param web.priorityClassName Priority Class Name\n  ## ref: https://kubernetes.io/docs/concepts/configuration/pod-priority-preemption/#priorityclass\n  ##\n  priorityClassName: \"\"\n  ## @param web.schedulerName Use an alternate scheduler, e.g. \"stork\".\n  ## ref: https://kubernetes.io/docs/tasks/administer-cluster/configure-multiple-schedulers/\n  ##\n  schedulerName: \"\"\n  ## @param web.terminationGracePeriodSeconds Seconds Airflow web pod needs to terminate gracefully\n  ## ref: https://kubernetes.io/docs/concepts/workloads/pods/pod/#termination-of-pods\n  ##\n  terminationGracePeriodSeconds: \"\"\n  ## @param web.updateStrategy.type Airflow web deployment strategy type\n  ## @param web.updateStrategy.rollingUpdate Airflow web deployment rolling update configuration parameters\n  ## ref: https://kubernetes.io/docs/concepts/workloads/controllers/deployment/#strategy\n  ##\n  updateStrategy:\n    type: RollingUpdate\n    rollingUpdate: {}\n  ## @param web.sidecars Add additional sidecar containers to the Airflow web pods\n  ## Example:\n  ## sidecars:\n  ##   - name: your-image-name\n  ##     image: your-image\n  ##     imagePullPolicy: Always\n  ##     ports:\n  ##       - name: portname\n  ##         containerPort: 1234\n  ##\n  sidecars: []\n  ## @param web.initContainers Add additional init containers to the Airflow web pods\n  ## Example:\n  ## initContainers:\n  ##   - name: your-image-name\n  ##     image: your-image\n  ##     imagePullPolicy: Always\n  ##     ports:\n  ##       - name: portname\n  ##         containerPort: 1234\n  ##\n  initContainers: []\n  ## @param web.extraVolumeMounts Optionally specify extra list of additional volumeMounts for the Airflow web pods\n  ##\n  extraVolumeMounts: []\n  ## @param web.extraVolumes Optionally specify extra list of additional volumes for the Airflow web pods\n  ##\n  extraVolumes: []\n  ## Airflow web Pod Disruption Budget\n  ## ref: https://kubernetes.io/docs/concepts/workloads/pods/disruptions/\n  ## @param web.pdb.create Deploy a pdb object for the Airflow web pods\n  ## @param web.pdb.minAvailable Maximum number/percentage of unavailable Airflow web replicas\n  ## @param web.pdb.maxUnavailable Maximum number/percentage of unavailable Airflow web replicas\n  ##\n  pdb:\n    create: false\n    minAvailable: 1\n    maxUnavailable: \"\"\n\n## @section Airflow scheduler parameters\n\nscheduler:\n  ## Bitnami Airflow Scheduler image version\n  ## ref: https://hub.docker.com/r/bitnami/airflow-scheduler/tags/\n  ## @param scheduler.image.registry Airflow Scheduler image registry\n  ## @param scheduler.image.repository Airflow Scheduler image repository\n  ## @param scheduler.image.tag Airflow Scheduler image tag (immutable tags are recommended)\n  ## @param scheduler.image.digest Airflow Schefuler image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag\n  ## @param scheduler.image.pullPolicy Airflow Scheduler image pull policy\n  ## @param scheduler.image.pullSecrets Airflow Scheduler image pull secrets\n  ## @param scheduler.image.debug Enable image debug mode\n  ##\n  image:\n    registry: docker.io\n    repository: bitnami/airflow-scheduler\n    tag: 2.4.2-debian-11-r4\n    digest: \"\"\n    ## Specify a imagePullPolicy\n    ## Defaults to 'Always' if image tag is 'latest', else set to 'IfNotPresent'\n    ## ref: https://kubernetes.io/docs/user-guide/images/#pre-pulling-images\n    ##\n    pullPolicy: IfNotPresent\n    ## Optionally specify an array of imagePullSecrets.\n    ## Secrets must be manually created in the namespace.\n    ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/\n    ## e.g:\n    ## pullSecrets:\n    ##   - myRegistryKeySecretName\n    ##\n    pullSecrets: []\n    ## Set to true if you would like to see extra information on logs\n    ##\n    debug: false\n  ## @param scheduler.replicaCount Number of scheduler replicas\n  ##\n  replicaCount: 1\n  ## @param scheduler.command Override cmd\n  ##\n  command: []\n  ## @param scheduler.args Override args\n  ##\n  args: []\n  ## @param scheduler.extraEnvVars Add extra environment variables\n  ##\n  extraEnvVars: []\n  ## @param scheduler.extraEnvVarsCM ConfigMap with extra environment variables\n  ##\n  extraEnvVarsCM: \"\"\n  ## @param scheduler.extraEnvVarsSecret Secret with extra environment variables\n  ##\n  extraEnvVarsSecret: \"\"\n  ## @param scheduler.extraEnvVarsSecrets List of secrets with extra environment variables for Airflow scheduler pods\n  ##\n  extraEnvVarsSecrets: []\n  ## @param scheduler.customLivenessProbe Custom livenessProbe that overrides the default one\n  ##\n  customLivenessProbe: {}\n  ## @param scheduler.customReadinessProbe Custom readinessProbe that overrides the default one\n  ##\n  customReadinessProbe: {}\n  ## @param scheduler.customStartupProbe Custom startupProbe that overrides the default one\n  ##\n  customStartupProbe: {}\n  ## Airflow scheduler resource requests and limits\n  ## ref: https://kubernetes.io/docs/user-guide/compute-resources/\n  ## @param scheduler.resources.limits The resources limits for the Airflow scheduler containers\n  ## @param scheduler.resources.requests The requested resources for the Airflow scheduler containers\n  ##\n  resources:\n    limits: {}\n    requests: {}\n  ## Configure Airflow scheduler pods Security Context\n  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod\n  ## @param scheduler.podSecurityContext.enabled Enabled Airflow scheduler pods' Security Context\n  ## @param scheduler.podSecurityContext.fsGroup Set Airflow scheduler pod's Security Context fsGroup\n  ##\n  podSecurityContext:\n    enabled: true\n    fsGroup: 1001\n  ## Configure Airflow scheduler containers (only main one) Security Context\n  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container\n  ## @param scheduler.containerSecurityContext.enabled Enabled Airflow scheduler containers' Security Context\n  ## @param scheduler.containerSecurityContext.runAsUser Set Airflow scheduler containers' Security Context runAsUser\n  ## @param scheduler.containerSecurityContext.runAsNonRoot Set Airflow scheduler containers' Security Context runAsNonRoot\n  ##\n  containerSecurityContext:\n    enabled: true\n    runAsUser: 1001\n    runAsNonRoot: true\n  ## @param scheduler.lifecycleHooks for the Airflow scheduler container(s) to automate configuration before or after startup\n  ##\n  lifecycleHooks: {}\n  ## @param scheduler.hostAliases Deployment pod host aliases\n  ## https://kubernetes.io/docs/concepts/services-networking/add-entries-to-pod-etc-hosts-with-host-aliases/\n  ##\n  hostAliases: []\n  ## @param scheduler.podLabels Add extra labels to the Airflow scheduler pods\n  ##\n  podLabels: {}\n  ## @param scheduler.podAnnotations Add extra annotations to the Airflow scheduler pods\n  ##\n  podAnnotations: {}\n  ## @param scheduler.affinity Affinity for Airflow scheduler pods assignment (evaluated as a template)\n  ## Ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity\n  ## Note: `scheduler.podAffinityPreset`, `scheduler.podAntiAffinityPreset`, and `scheduler.nodeAffinityPreset` will be ignored when it's set\n  ##\n  affinity: {}\n  ## Node affinity preset\n  ## Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#node-affinity\n  ## @param scheduler.nodeAffinityPreset.key Node label key to match. Ignored if `scheduler.affinity` is set.\n  ## @param scheduler.nodeAffinityPreset.type Node affinity preset type. Ignored if `scheduler.affinity` is set. Allowed values: `soft` or `hard`\n  ## @param scheduler.nodeAffinityPreset.values Node label values to match. Ignored if `scheduler.affinity` is set.\n  ##\n  nodeAffinityPreset:\n    ## e.g:\n    ## key: \"kubernetes.io/e2e-az-name\"\n    ##\n    key: \"\"\n    type: \"\"\n    ## e.g:\n    ## values:\n    ##   - e2e-az1\n    ##   - e2e-az2\n    ##\n    values: []\n  ## @param scheduler.nodeSelector Node labels for Airflow scheduler pods assignment\n  ## Ref: https://kubernetes.io/docs/user-guide/node-selection/\n  ##\n  nodeSelector: {}\n  ## @param scheduler.podAffinityPreset Pod affinity preset. Ignored if `scheduler.affinity` is set. Allowed values: `soft` or `hard`.\n  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity\n  ##\n  podAffinityPreset: \"\"\n  ## @param scheduler.podAntiAffinityPreset Pod anti-affinity preset. Ignored if `scheduler.affinity` is set. Allowed values: `soft` or `hard`.\n  ## Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity\n  ##\n  podAntiAffinityPreset: soft\n  ## @param scheduler.tolerations Tolerations for Airflow scheduler pods assignment\n  ## Ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/\n  ##\n  tolerations: []\n  ## @param scheduler.topologySpreadConstraints Topology Spread Constraints for pod assignment spread across your cluster among failure-domains. Evaluated as a template\n  ## Ref: https://kubernetes.io/docs/concepts/workloads/pods/pod-topology-spread-constraints/#spread-constraints-for-pods\n  ##\n  topologySpreadConstraints: []\n  ## @param scheduler.priorityClassName Priority Class Name\n  ## ref: https://kubernetes.io/docs/concepts/configuration/pod-priority-preemption/#priorityclass\n  ##\n  priorityClassName: \"\"\n  ## @param scheduler.schedulerName Use an alternate scheduler, e.g. \"stork\".\n  ## ref: https://kubernetes.io/docs/tasks/administer-cluster/configure-multiple-schedulers/\n  ##\n  schedulerName: \"\"\n  ## @param scheduler.terminationGracePeriodSeconds Seconds Airflow scheduler pod needs to terminate gracefully\n  ## ref: https://kubernetes.io/docs/concepts/workloads/pods/pod/#termination-of-pods\n  ##\n  terminationGracePeriodSeconds: \"\"\n  ## @param scheduler.updateStrategy.type Airflow scheduler deployment strategy type\n  ## @param scheduler.updateStrategy.rollingUpdate Airflow scheduler deployment rolling update configuration parameters\n  ## ref: https://kubernetes.io/docs/concepts/workloads/controllers/deployment/#strategy\n  ##\n  updateStrategy:\n    type: RollingUpdate\n    rollingUpdate: {}\n  ## @param scheduler.sidecars Add additional sidecar containers to the Airflow scheduler pods\n  ## Example:\n  ## sidecars:\n  ##   - name: your-image-name\n  ##     image: your-image\n  ##     imagePullPolicy: Always\n  ##     ports:\n  ##       - name: portname\n  ##         containerPort: 1234\n  ##\n  sidecars: []\n  ## @param scheduler.initContainers Add additional init containers to the Airflow scheduler pods\n  ## Example:\n  ## initContainers:\n  ##   - name: your-image-name\n  ##     image: your-image\n  ##     imagePullPolicy: Always\n  ##     ports:\n  ##       - name: portname\n  ##         containerPort: 1234\n  ##\n  initContainers: []\n  ## @param scheduler.extraVolumeMounts Optionally specify extra list of additional volumeMounts for the Airflow scheduler pods\n  ##\n  extraVolumeMounts: []\n  ## @param scheduler.extraVolumes Optionally specify extra list of additional volumes for the Airflow scheduler pods\n  ##\n  extraVolumes: []\n  ## Airflow scheduler Pod Disruption Budget\n  ## ref: https://kubernetes.io/docs/concepts/workloads/pods/disruptions/\n  ## @param scheduler.pdb.create Deploy a pdb object for the Airflow scheduler pods\n  ## @param scheduler.pdb.minAvailable Maximum number/percentage of unavailable Airflow scheduler replicas\n  ## @param scheduler.pdb.maxUnavailable Maximum number/percentage of unavailable Airflow scheduler replicas\n  ##\n  pdb:\n    create: false\n    minAvailable: 1\n    maxUnavailable: \"\"\n\n## @section Airflow worker parameters\n\nworker:\n  ## Bitnami Airflow Worker image version\n  ## ref: https://hub.docker.com/r/bitnami/airflow-worker/tags/\n  ## @param worker.image.registry Airflow Worker image registry\n  ## @param worker.image.repository Airflow Worker image repository\n  ## @param worker.image.tag Airflow Worker image tag (immutable tags are recommended)\n  ## @param worker.image.digest Airflow Worker image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag\n  ## @param worker.image.pullPolicy Airflow Worker image pull policy\n  ## @param worker.image.pullSecrets Airflow Worker image pull secrets\n  ## @param worker.image.debug Enable image debug mode\n  ##\n  image:\n    registry: docker.io\n    repository: bitnami/airflow-worker\n    tag: 2.4.2-debian-11-r4\n    digest: \"\"\n    ## Specify a imagePullPolicy\n    ## Defaults to 'Always' if image tag is 'latest', else set to 'IfNotPresent'\n    ## ref: https://kubernetes.io/docs/user-guide/images/#pre-pulling-images\n    ##\n    pullPolicy: IfNotPresent\n    ## Optionally specify an array of imagePullSecrets.\n    ## Secrets must be manually created in the namespace.\n    ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/\n    ## e.g:\n    ## pullSecrets:\n    ##   - myRegistryKeySecretName\n    ##\n    pullSecrets: []\n    ## Set to true if you would like to see extra information on logs\n    ##\n    debug: false\n  ## @param worker.command Override default container command (useful when using custom images)\n  ##\n  command: []\n  ## @param worker.args Override default container args (useful when using custom images)\n  ##\n  args: []\n  ## @param worker.extraEnvVars Array with extra environment variables to add Airflow worker pods\n  ##\n  extraEnvVars: []\n  ## @param worker.extraEnvVarsCM ConfigMap containing extra environment variables for Airflow worker pods\n  ##\n  extraEnvVarsCM: \"\"\n  ## @param worker.extraEnvVarsSecret Secret containing extra environment variables (in case of sensitive data) for Airflow worker pods\n  ##\n  extraEnvVarsSecret: \"\"\n  ## @param worker.extraEnvVarsSecrets List of secrets with extra environment variables for Airflow worker pods\n  ##\n  extraEnvVarsSecrets: []\n  ## @param worker.containerPorts.http Airflow worker HTTP container port\n  ##\n  containerPorts:\n    http: 8793\n  ## @param worker.replicaCount Number of Airflow worker replicas\n  ##\n  replicaCount: 1\n  ## Configure extra options for Airflow worker containers' liveness, readiness and startup probes\n  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/#configure-probes\n  ## @param worker.livenessProbe.enabled Enable livenessProbe on Airflow worker containers\n  ## @param worker.livenessProbe.initialDelaySeconds Initial delay seconds for livenessProbe\n  ## @param worker.livenessProbe.periodSeconds Period seconds for livenessProbe\n  ## @param worker.livenessProbe.timeoutSeconds Timeout seconds for livenessProbe\n  ## @param worker.livenessProbe.failureThreshold Failure threshold for livenessProbe\n  ## @param worker.livenessProbe.successThreshold Success threshold for livenessProbe\n  ##\n  livenessProbe:\n    enabled: true\n    initialDelaySeconds: 180\n    periodSeconds: 20\n    timeoutSeconds: 5\n    failureThreshold: 6\n    successThreshold: 1\n  ## @param worker.readinessProbe.enabled Enable readinessProbe on Airflow worker containers\n  ## @param worker.readinessProbe.initialDelaySeconds Initial delay seconds for readinessProbe\n  ## @param worker.readinessProbe.periodSeconds Period seconds for readinessProbe\n  ## @param worker.readinessProbe.timeoutSeconds Timeout seconds for readinessProbe\n  ## @param worker.readinessProbe.failureThreshold Failure threshold for readinessProbe\n  ## @param worker.readinessProbe.successThreshold Success threshold for readinessProbe\n  ##\n  readinessProbe:\n    enabled: true\n    initialDelaySeconds: 30\n    periodSeconds: 10\n    timeoutSeconds: 5\n    failureThreshold: 6\n    successThreshold: 1\n  ## @param worker.startupProbe.enabled Enable startupProbe on Airflow worker containers\n  ## @param worker.startupProbe.initialDelaySeconds Initial delay seconds for startupProbe\n  ## @param worker.startupProbe.periodSeconds Period seconds for startupProbe\n  ## @param worker.startupProbe.timeoutSeconds Timeout seconds for startupProbe\n  ## @param worker.startupProbe.failureThreshold Failure threshold for startupProbe\n  ## @param worker.startupProbe.successThreshold Success threshold for startupProbe\n  ##\n  startupProbe:\n    enabled: false\n    initialDelaySeconds: 60\n    periodSeconds: 10\n    timeoutSeconds: 1\n    failureThreshold: 15\n    successThreshold: 1\n  ## @param worker.customLivenessProbe Custom livenessProbe that overrides the default one\n  ##\n  customLivenessProbe: {}\n  ## @param worker.customReadinessProbe Custom readinessProbe that overrides the default one\n  ##\n  customReadinessProbe: {}\n  ## @param worker.customStartupProbe Custom startupProbe that overrides the default one\n  ##\n  customStartupProbe: {}\n  ## Airflow worker resource requests and limits\n  ## ref: https://kubernetes.io/docs/user-guide/compute-resources/\n  ## @param worker.resources.limits The resources limits for the Airflow worker containers\n  ## @param worker.resources.requests The requested resources for the Airflow worker containers\n  ##\n  resources:\n    limits: {}\n    requests: {}\n  ## Configure Airflow worker pods Security Context\n  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod\n  ## @param worker.podSecurityContext.enabled Enabled Airflow worker pods' Security Context\n  ## @param worker.podSecurityContext.fsGroup Set Airflow worker pod's Security Context fsGroup\n  ##\n  podSecurityContext:\n    enabled: true\n    fsGroup: 1001\n  ## Configure Airflow worker containers (only main one) Security Context\n  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container\n  ## @param worker.containerSecurityContext.enabled Enabled Airflow worker containers' Security Context\n  ## @param worker.containerSecurityContext.runAsUser Set Airflow worker containers' Security Context runAsUser\n  ## @param worker.containerSecurityContext.runAsNonRoot Set Airflow worker containers' Security Context runAsNonRoot\n  ##\n  containerSecurityContext:\n    enabled: true\n    runAsUser: 1001\n    runAsNonRoot: true\n  ## @param worker.lifecycleHooks for the Airflow worker container(s) to automate configuration before or after startup\n  ##\n  lifecycleHooks: {}\n  ## @param worker.hostAliases Deployment pod host aliases\n  ## https://kubernetes.io/docs/concepts/services-networking/add-entries-to-pod-etc-hosts-with-host-aliases/\n  ##\n  hostAliases: []\n  ## @param worker.podLabels Add extra labels to the Airflow worker pods\n  ##\n  podLabels: {}\n  ## @param worker.podAnnotations Add extra annotations to the Airflow worker pods\n  ##\n  podAnnotations: {}\n  ## @param worker.affinity Affinity for Airflow worker pods assignment (evaluated as a template)\n  ## Ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity\n  ## Note: `worker.podAffinityPreset`, `worker.podAntiAffinityPreset`, and `worker.nodeAffinityPreset` will be ignored when it's set\n  ##\n  affinity: {}\n  ## Node affinity preset\n  ## Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#node-affinity\n  ## @param worker.nodeAffinityPreset.key Node label key to match. Ignored if `worker.affinity` is set.\n  ## @param worker.nodeAffinityPreset.type Node affinity preset type. Ignored if `worker.affinity` is set. Allowed values: `soft` or `hard`\n  ## @param worker.nodeAffinityPreset.values Node label values to match. Ignored if `worker.affinity` is set.\n  ##\n  nodeAffinityPreset:\n    ## e.g:\n    ## key: \"kubernetes.io/e2e-az-name\"\n    ##\n    key: \"\"\n    type: \"\"\n    ## e.g:\n    ## values:\n    ##   - e2e-az1\n    ##   - e2e-az2\n    ##\n    values: []\n  ## @param worker.nodeSelector Node labels for Airflow worker pods assignment\n  ## Ref: https://kubernetes.io/docs/user-guide/node-selection/\n  ##\n  nodeSelector: {}\n  ## @param worker.podAffinityPreset Pod affinity preset. Ignored if `worker.affinity` is set. Allowed values: `soft` or `hard`.\n  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity\n  ##\n  podAffinityPreset: \"\"\n  ## @param worker.podAntiAffinityPreset Pod anti-affinity preset. Ignored if `worker.affinity` is set. Allowed values: `soft` or `hard`.\n  ## Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity\n  ##\n  podAntiAffinityPreset: soft\n  ## @param worker.tolerations Tolerations for Airflow worker pods assignment\n  ## Ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/\n  ##\n  tolerations: []\n  ## @param worker.topologySpreadConstraints Topology Spread Constraints for pod assignment spread across your cluster among failure-domains. Evaluated as a template\n  ## Ref: https://kubernetes.io/docs/concepts/workloads/pods/pod-topology-spread-constraints/#spread-constraints-for-pods\n  ##\n  topologySpreadConstraints: []\n  ## @param worker.priorityClassName Priority Class Name\n  ## ref: https://kubernetes.io/docs/concepts/configuration/pod-priority-preemption/#priorityclass\n  ##\n  priorityClassName: \"\"\n  ## @param worker.schedulerName Use an alternate scheduler, e.g. \"stork\".\n  ## ref: https://kubernetes.io/docs/tasks/administer-cluster/configure-multiple-schedulers/\n  ##\n  schedulerName: \"\"\n  ## @param worker.terminationGracePeriodSeconds Seconds Airflow worker pod needs to terminate gracefully\n  ## ref: https://kubernetes.io/docs/concepts/workloads/pods/pod/#termination-of-pods\n  ##\n  terminationGracePeriodSeconds: \"\"\n  ## @param worker.updateStrategy.type Airflow worker deployment strategy type\n  ## @param worker.updateStrategy.rollingUpdate Airflow worker deployment rolling update configuration parameters\n  ## ref: https://kubernetes.io/docs/concepts/workloads/controllers/deployment/#strategy\n  ##\n  updateStrategy:\n    type: RollingUpdate\n    rollingUpdate: {}\n  ## @param worker.sidecars Add additional sidecar containers to the Airflow worker pods\n  ## Example:\n  ## sidecars:\n  ##   - name: your-image-name\n  ##     image: your-image\n  ##     imagePullPolicy: Always\n  ##     ports:\n  ##       - name: portname\n  ##         containerPort: 1234\n  ##\n  sidecars: []\n  ## @param worker.initContainers Add additional init containers to the Airflow worker pods\n  ## Example:\n  ## initContainers:\n  ##   - name: your-image-name\n  ##     image: your-image\n  ##     imagePullPolicy: Always\n  ##     ports:\n  ##       - name: portname\n  ##         containerPort: 1234\n  ##\n  initContainers: []\n  ## @param worker.extraVolumeMounts Optionally specify extra list of additional volumeMounts for the Airflow worker pods\n  ##\n  extraVolumeMounts: []\n  ## @param worker.extraVolumes Optionally specify extra list of additional volumes for the Airflow worker pods\n  ##\n  extraVolumes: []\n  ## @param worker.extraVolumeClaimTemplates Optionally specify extra list of volumesClaimTemplates for the Airflow worker statefulset\n  ##\n  extraVolumeClaimTemplates: []\n  ## @param worker.podTemplate Template to replace the default one to be use when `executor=KubernetesExecutor` to create Airflow worker pods\n  ##\n  podTemplate: {}\n  ## Airflow worker Pod Disruption Budget\n  ## ref: https://kubernetes.io/docs/concepts/workloads/pods/disruptions/\n  ## @param worker.pdb.create Deploy a pdb object for the Airflow worker pods\n  ## @param worker.pdb.minAvailable Maximum number/percentage of unavailable Airflow worker replicas\n  ## @param worker.pdb.maxUnavailable Maximum number/percentage of unavailable Airflow worker replicas\n  ##\n  pdb:\n    create: false\n    minAvailable: 1\n    maxUnavailable: \"\"\n  ## Enable HorizontalPodAutoscaler for worker pods\n  ## ref: https://kubernetes.io/docs/tasks/run-application/horizontal-pod-autoscale/\n  ## @param worker.autoscaling.enabled Whether enable horizontal pod autoscaler\n  ## @param worker.autoscaling.minReplicas Configure a minimum amount of pods\n  ## @param worker.autoscaling.maxReplicas Configure a maximum amount of pods\n  ## @param worker.autoscaling.targetCPU Define the CPU target to trigger the scaling actions (utilization percentage)\n  ## @param worker.autoscaling.targetMemory Define the memory target to trigger the scaling actions (utilization percentage)\n  ##\n  autoscaling:\n    enabled: false\n    minReplicas: 1\n    maxReplicas: 3\n    targetCPU: 80\n    targetMemory: 80\n\n## @section Airflow git sync parameters\n\n## Configure Git to pull dags and plugins\n##\ngit:\n  ## Bitnami Git image version\n  ## ref: https://hub.docker.com/r/bitnami/git/tags/\n  ## @param git.image.registry Git image registry\n  ## @param git.image.repository Git image repository\n  ## @param git.image.tag Git image tag (immutable tags are recommended)\n  ## @param git.image.digest Git image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag\n  ## @param git.image.pullPolicy Git image pull policy\n  ## @param git.image.pullSecrets Git image pull secrets\n  ##\n  image:\n    registry: docker.io\n    repository: bitnami/git\n    tag: 2.38.1-debian-11-r7\n    digest: \"\"\n    ## Specify a imagePullPolicy\n    ## Defaults to 'Always' if image tag is 'latest', else set to 'IfNotPresent'\n    ## ref: https://kubernetes.io/docs/user-guide/images/#pre-pulling-images\n    ##\n    pullPolicy: IfNotPresent\n    ## Optionally specify an array of imagePullSecrets.\n    ## Secrets must be manually created in the namespace.\n    ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/\n    ## e.g:\n    ## pullSecrets:\n    ##   - myRegistryKeySecretName\n    ##\n    pullSecrets: []\n  ## Get DAG files from git repositories\n  ## @param git.dags.enabled Enable in order to download DAG files from git repositories.\n  ## @param git.dags.repositories [array] Array of repositories from which to download DAG files\n  ##\n  dags:\n    enabled: false\n    ## Name for repositories can be anything unique and must follow same naming conventions as kubernetes.\n    ## Kubernetes resources can have names up to 253 characters long. The characters allowed in names are:\n    ## digits (0-9), lower case letters (a-z), -, and .\n    ## Example:\n    ##   - repository: https://github.com/myuser/myrepo\n    ##     branch: main\n    ##     name: my-dags\n    ##     path: /\n    ##\n    repositories:\n      - repository: \"\"\n        ## Branch from repository to checkout\n        ##\n        branch: \"\"\n        ## An unique identifier for repository, must be unique for each repository\n        ##\n        name: \"\"\n        ## Path to a folder in the repository containing the dags\n        ##\n        path: \"\"\n  ## Get Plugins files from git repositories.\n  ## @param git.plugins.enabled Enable in order to download Plugins files from git repositories.\n  ## @param git.plugins.repositories [array] Array of repositories from which to download DAG files\n  ##\n  plugins:\n    enabled: false\n    repositories:\n      - repository: \"\"\n        ## Branch from repository to checkout\n        ##\n        branch: \"\"\n        ## An unique identifier for repository, must be unique for each repository\n        ##\n        name: \"\"\n        ## Path to a folder in the repository containing the plugins\n        ##\n        path: \"\"\n  ## Properties for the Clone init container\n  ## @param git.clone.command Override cmd\n  ## @param git.clone.args Override args\n  ## @param git.clone.extraVolumeMounts Add extra volume mounts\n  ## @param git.clone.extraEnvVars Add extra environment variables\n  ## @param git.clone.extraEnvVarsCM ConfigMap with extra environment variables\n  ## @param git.clone.extraEnvVarsSecret Secret with extra environment variables\n  ## @param git.clone.resources Clone init container resource requests and limits\n  ##\n  clone:\n    command: []\n    args: []\n    extraVolumeMounts: []\n    extraEnvVars: []\n    extraEnvVarsCM: \"\"\n    extraEnvVarsSecret: \"\"\n    ## Clone init container resource requests and limits\n    ## ref: https://kubernetes.io/docs/user-guide/compute-resources/\n    ##\n    resources: {}\n  ## Properties for the Sync sidecar container\n  ## @param git.sync.interval Interval in seconds to pull the git repository containing the plugins and/or DAG files\n  ## @param git.sync.command Override cmd\n  ## @param git.sync.args Override args\n  ## @param git.sync.extraVolumeMounts Add extra volume mounts\n  ## @param git.sync.extraEnvVars Add extra environment variables\n  ## @param git.sync.extraEnvVarsCM ConfigMap with extra environment variables\n  ## @param git.sync.extraEnvVarsSecret Secret with extra environment variables\n  ## @param git.sync.resources Sync sidecar container resource requests and limits\n  ##\n  sync:\n    interval: 60\n    command: []\n    args: []\n    extraVolumeMounts: []\n    extraEnvVars: []\n    extraEnvVarsCM: \"\"\n    extraEnvVarsSecret: \"\"\n    ## Sync sidecar container resource requests and limits\n    ## ref: https://kubernetes.io/docs/user-guide/compute-resources/\n    ##\n    resources: {}\n\n## @section Airflow ldap parameters\n\n## LDAP configuration\n## @param ldap.enabled Enable LDAP authentication\n## @param ldap.uri Server URI, eg. ldap://ldap_server:389\n## DEPRECATED ldap.base It will be removed in a future release, please use 'ldap.basedn' instead\n## @param ldap.basedn Base of the search, eg. ou=example,o=org.\n## DEPRECATED ldap.uidField It will be removed in a future release,, please use 'ldap.searchAttribute' instead\n## @param ldap.searchAttribute if doing an indirect bind to ldap, this is the field that matches the username when searching for the account to bind to\n## @param ldap.binddn DN of the account used to search in the LDAP server.\n## @param ldap.bindpw Bind Password\n## @param ldap.userRegistration Set to True to enable user self registration\n## @param ldap.userRegistrationRole Set role name to be assign when a user registers himself. This role must already exist. Mandatory when using ldap.userRegistration\n## @param ldap.rolesMapping mapping from LDAP DN to a list of roles\n## @param ldap.rolesSyncAtLogin replace ALL the user's roles each login, or only on registration\n##\nldap:\n  enabled: false\n  uri: \"ldap://ldap_server:389\"\n  basedn: \"dc=example,dc=org\"\n  searchAttribute: \"cn\"\n  binddn: \"cn=admin,dc=example,dc=org\"\n  bindpw: \"\"\n  userRegistration: 'True'\n  userRegistrationRole: \"Public\"\n  rolesMapping: '{ \"cn=All,ou=Groups,dc=example,dc=org\": [\"User\"], \"cn=Admins,ou=Groups,dc=example,dc=org\": [\"Admin\"], }'\n  rolesSyncAtLogin: 'True'\n\n  ## SSL/TLS parameters for LDAP\n  ## @param ldap.tls.enabled Enabled TLS/SSL for LDAP, you must include the CA file.\n  ## @param ldap.tls.allowSelfSigned Allow to use self signed certificates\n  ## DEPRECATED ldap.tls.CAcertificateSecret It will be removed in a future release, please use ldap.tls.certificatesSecret instead\n  ## @param ldap.tls.certificatesSecret Name of the existing secret containing the certificate CA file that will be used by ldap client\n  ## @param ldap.tls.certificatesMountPath Where LDAP certifcates are mounted.\n  ## DEPRECATED ldap.tls.CAcertificateFilename It will be removed in a future release, please use ldap.tls.CAFilename instead\n  ## @param ldap.tls.CAFilename LDAP CA cert filename\n  ##\n  tls:\n    enabled: false\n    allowSelfSigned: true\n    certificatesSecret: \"\"\n    certificatesMountPath: /opt/bitnami/airflow/conf/certs\n    CAFilename: \"\"\n\n## @section Traffic Exposure Parameters\n\n## Airflow service parameters\n##\nservice:\n  ## @param service.type Airflow service type\n  ##\n  type: ClusterIP\n  ## @param service.ports.http Airflow service HTTP port\n  ##\n  ports:\n    http: 8080\n  ## Node ports to expose\n  ## @param service.nodePorts.http Node port for HTTP\n  ## NOTE: choose port between <30000-32767>\n  ##\n  nodePorts:\n    http: \"\"\n  ## @param service.sessionAffinity Control where client requests go, to the same pod or round-robin\n  ## Values: ClientIP or None\n  ## ref: https://kubernetes.io/docs/user-guide/services/\n  ##\n  sessionAffinity: None\n  ## @param service.sessionAffinityConfig Additional settings for the sessionAffinity\n  ## sessionAffinityConfig:\n  ##   clientIP:\n  ##     timeoutSeconds: 300\n  ##\n  sessionAffinityConfig: {}\n  ## @param service.clusterIP Airflow service Cluster IP\n  ## e.g.:\n  ## clusterIP: None\n  ##\n  clusterIP: \"\"\n  ## @param service.loadBalancerIP Airflow service Load Balancer IP\n  ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-loadbalancer\n  ##\n  loadBalancerIP: \"\"\n  ## @param service.loadBalancerSourceRanges Airflow service Load Balancer sources\n  ## ref: https://kubernetes.io/docs/tasks/access-application-cluster/configure-cloud-provider-firewall/#restrict-access-for-loadbalancer-service\n  ## e.g:\n  ## loadBalancerSourceRanges:\n  ##   - **********/24\n  ##\n  loadBalancerSourceRanges: []\n  ## @param service.externalTrafficPolicy Airflow service external traffic policy\n  ## ref https://kubernetes.io/docs/tasks/access-application-cluster/create-external-load-balancer/#preserving-the-client-source-ip\n  ##\n  externalTrafficPolicy: Cluster\n  ## @param service.annotations Additional custom annotations for Airflow service\n  ##\n  annotations: {}\n  ## @param service.extraPorts Extra port to expose on Airflow service\n  ##\n  extraPorts: []\n\n## Airflow ingress parameters\n## ref: https://kubernetes.io/docs/user-guide/ingress/\n##\ningress:\n  ## @param ingress.enabled Enable ingress record generation for Airflow\n  ##\n  enabled: false\n  ## @param ingress.ingressClassName IngressClass that will be be used to implement the Ingress (Kubernetes 1.18+)\n  ## This is supported in Kubernetes 1.18+ and required if you have more than one IngressClass marked as the default for your cluster .\n  ## ref: https://kubernetes.io/blog/2020/04/02/improvements-to-the-ingress-api-in-kubernetes-1.18/\n  ##\n  ingressClassName: \"\"\n  ## @param ingress.pathType Ingress path type\n  ##\n  pathType: ImplementationSpecific\n  ## @param ingress.apiVersion Force Ingress API version (automatically detected if not set)\n  ##\n  apiVersion: \"\"\n  ## @param ingress.hostname Default host for the ingress record\n  ##\n  hostname: airflow.local\n  ## @param ingress.path Default path for the ingress record\n  ## NOTE: You may need to set this to '/*' in order to use this with ALB ingress controllers\n  ##\n  path: /\n  ## @param ingress.annotations [object] Additional annotations for the Ingress resource. To enable certificate autogeneration, place here your cert-manager annotations.\n  ## Use this parameter to set the required annotations for cert-manager, see\n  ## ref: https://cert-manager.io/docs/usage/ingress/#supported-annotations\n  ## e.g:\n  ## annotations:\n  ##   kubernetes.io/ingress.class: nginx\n  ##   cert-manager.io/cluster-issuer: cluster-issuer-name\n  ##\n  annotations: {}\n  ## @param ingress.tls Enable TLS configuration for the host defined at `ingress.hostname` parameter\n  ## TLS certificates will be retrieved from a TLS secret with name: `{{- printf \"%s-tls\" .Values.ingress.hostname }}`\n  ## You can:\n  ##   - Use the `ingress.secrets` parameter to create this TLS secret\n  ##   - Rely on cert-manager to create it by setting the corresponding annotations\n  ##   - Rely on Helm to create self-signed certificates by setting `ingress.selfSigned=true`\n  ##\n  tls: false\n  ## @param ingress.selfSigned Create a TLS secret for this ingress record using self-signed certificates generated by Helm\n  ##\n  selfSigned: false\n  ## @param ingress.extraHosts An array with additional hostname(s) to be covered with the ingress record\n  ## e.g:\n  ## extraHosts:\n  ##   - name: airflow.local\n  ##     path: /\n  ##\n  extraHosts: []\n  ## @param ingress.extraPaths An array with additional arbitrary paths that may need to be added to the ingress under the main host\n  ## e.g:\n  ## extraPaths:\n  ## - path: /*\n  ##   backend:\n  ##     serviceName: ssl-redirect\n  ##     servicePort: use-annotation\n  ##\n  extraPaths: []\n  ## @param ingress.extraTls TLS configuration for additional hostname(s) to be covered with this ingress record\n  ## ref: https://kubernetes.io/docs/concepts/services-networking/ingress/#tls\n  ## e.g:\n  ## extraTls:\n  ## - hosts:\n  ##     - airflow.local\n  ##   secretName: airflow.local-tls\n  ##\n  extraTls: []\n  ## @param ingress.secrets Custom TLS certificates as secrets\n  ## NOTE: 'key' and 'certificate' are expected in PEM format\n  ## NOTE: 'name' should line up with a 'secretName' set further up\n  ## If it is not set and you're using cert-manager, this is unneeded, as it will create a secret for you with valid certificates\n  ## If it is not set and you're NOT using cert-manager either, self-signed certificates will be created valid for 365 days\n  ## It is also possible to create and manage the certificates outside of this helm chart\n  ## Please see README.md for more information\n  ## e.g:\n  ## secrets:\n  ##   - name: airflow.local-tls\n  ##     key: |-\n  ##       -----BEGIN RSA PRIVATE KEY-----\n  ##       ...\n  ##       -----END RSA PRIVATE KEY-----\n  ##     certificate: |-\n  ##       -----BEGIN CERTIFICATE-----\n  ##       ...\n  ##       -----END CERTIFICATE-----\n  ##\n  secrets: []\n  ## @param ingress.extraRules Additional rules to be covered with this ingress record\n  ## ref: https://kubernetes.io/docs/concepts/services-networking/ingress/#ingress-rules\n  ## e.g:\n  ## extraRules:\n  ## - host: example.local\n  ##     http:\n  ##       path: /\n  ##       backend:\n  ##         service:\n  ##           name: example-svc\n  ##           port:\n  ##             name: http\n  ##\n  extraRules: []\n\n## @section Other Parameters\n\n## Service account for Airflow pods to use.\n## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-service-account/\n##\nserviceAccount:\n  ## @param serviceAccount.create Enable creation of ServiceAccount for Airflow pods\n  ##\n  create: false\n  ## @param serviceAccount.name The name of the ServiceAccount to use.\n  ## If not set and create is true, a name is generated using the common.names.fullname template\n  ##\n  name: \"\"\n  ## @param serviceAccount.automountServiceAccountToken Allows auto mount of ServiceAccountToken on the serviceAccount created\n  ## Can be set to false if pods using this serviceAccount do not need to use K8s API\n  ##\n  automountServiceAccountToken: true\n  ## @param serviceAccount.annotations Additional custom annotations for the ServiceAccount\n  ##\n  annotations: {}\n## Role Based Access\n## Ref: https://kubernetes.io/docs/admin/authorization/rbac/\n## @param rbac.create Create Role and RoleBinding\n##\nrbac:\n  create: false\n  ## @param rbac.rules Custom RBAC rules to set\n  ## e.g:\n  ## rules:\n  ##   - apiGroups:\n  ##       - \"\"\n  ##     resources:\n  ##       - pods\n  ##     verbs:\n  ##       - get\n  ##       - list\n  ##\n  rules: []\n\n## @section Airflow metrics parameters\n\nmetrics:\n  ## @param metrics.enabled Whether or not to create a standalone Airflow exporter to expose Airflow metrics\n  ##\n  enabled: false\n  ## Bitnami Airflow exporter image\n  ## ref: https://hub.docker.com/r/bitnami/airflow-exporter/tags/\n  ## @param metrics.image.registry Airflow exporter image registry\n  ## @param metrics.image.repository Airflow exporter image repository\n  ## @param metrics.image.tag Airflow exporter image tag (immutable tags are recommended)\n  ## @param metrics.image.digest Airflow exporter image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag\n  ## @param metrics.image.pullPolicy Airflow exporter image pull policy\n  ## @param metrics.image.pullSecrets Airflow exporter image pull secrets\n  ##\n  image:\n    registry: docker.io\n    repository: bitnami/airflow-exporter\n    tag: 0.20220314.0-debian-11-r57\n    digest: \"\"\n    pullPolicy: IfNotPresent\n    ## Optionally specify an array of imagePullSecrets.\n    ## Secrets must be manually created in the namespace.\n    ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/\n    ## e.g:\n    ## pullSecrets:\n    ##   - myRegistryKeySecretName\n    ##\n    pullSecrets: []\n  ## @param metrics.extraEnvVars Array with extra environment variables to add Airflow exporter pods\n  ##\n  extraEnvVars: []\n  ## @param metrics.extraEnvVarsCM ConfigMap containing extra environment variables for Airflow exporter pods\n  ##\n  extraEnvVarsCM: \"\"\n  ## @param metrics.extraEnvVarsSecret Secret containing extra environment variables (in case of sensitive data) for Airflow exporter pods\n  ##\n  extraEnvVarsSecret: \"\"\n  ## @param metrics.containerPorts.http Airflow exporter metrics container port\n  ##\n  containerPorts:\n    http: 9112\n  ## Airflow exporter resource requests and limits\n  ## ref: https://kubernetes.io/docs/user-guide/compute-resources/\n  ## @param metrics.resources.limits The resources limits for the container\n  ## @param metrics.resources.requests The requested resources for the container\n  ##\n  resources:\n    limits: {}\n    requests: {}\n  ## Airflow exporter pods' Security Context\n  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod\n  ## @param metrics.podSecurityContext.enabled Enable security context for the pods\n  ## @param metrics.podSecurityContext.fsGroup Set Airflow exporter pod's Security Context fsGroup\n  ##\n  podSecurityContext:\n    enabled: true\n    fsGroup: 1001\n  ## Airflow exporter containers' Security Context\n  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container\n  ## @param metrics.containerSecurityContext.enabled Enable Airflow exporter containers' Security Context\n  ## @param metrics.containerSecurityContext.runAsUser Set Airflow exporter containers' Security Context runAsUser\n  ## @param metrics.containerSecurityContext.runAsNonRoot Set Airflow exporter containers' Security Context runAsNonRoot\n  ## e.g:\n  ##   containerSecurityContext:\n  ##     enabled: true\n  ##     capabilities:\n  ##       drop: [\"NET_RAW\"]\n  ##     readOnlyRootFilesystem: true\n  ##\n  containerSecurityContext:\n    enabled: true\n    runAsUser: 1001\n    runAsNonRoot: true\n  ## @param metrics.lifecycleHooks for the Airflow exporter container(s) to automate configuration before or after startup\n  ##\n  lifecycleHooks: {}\n  ## @param metrics.hostAliases Airflow exporter pods host aliases\n  ## https://kubernetes.io/docs/concepts/services-networking/add-entries-to-pod-etc-hosts-with-host-aliases/\n  ##\n  hostAliases: []\n  ## @param metrics.podLabels Extra labels for Airflow exporter pods\n  ## Ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/\n  ##\n  podLabels: {}\n  ## @param metrics.podAnnotations Extra annotations for Airflow exporter pods\n  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/\n  ##\n  podAnnotations: {}\n  ## @param metrics.podAffinityPreset Pod affinity preset. Ignored if `metrics.affinity` is set. Allowed values: `soft` or `hard`\n  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity\n  ##\n  podAffinityPreset: \"\"\n  ## @param metrics.podAntiAffinityPreset Pod anti-affinity preset. Ignored if `metrics.affinity` is set. Allowed values: `soft` or `hard`\n  ## Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity\n  ##\n  podAntiAffinityPreset: soft\n  ## Node metrics.affinity preset\n  ## Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#node-affinity\n  ##\n  nodeAffinityPreset:\n    ## @param metrics.nodeAffinityPreset.type Node affinity preset type. Ignored if `metrics.affinity` is set. Allowed values: `soft` or `hard`\n    ##\n    type: \"\"\n    ## @param metrics.nodeAffinityPreset.key Node label key to match Ignored if `metrics.affinity` is set.\n    ## E.g.\n    ## key: \"kubernetes.io/e2e-az-name\"\n    ##\n    key: \"\"\n    ## @param metrics.nodeAffinityPreset.values Node label values to match. Ignored if `metrics.affinity` is set.\n    ## E.g.\n    ## values:\n    ##   - e2e-az1\n    ##   - e2e-az2\n    ##\n    values: []\n  ## @param metrics.affinity Affinity for pod assignment\n  ## Ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity\n  ## Note: metrics.podAffinityPreset, metrics.podAntiAffinityPreset, and metrics.nodeAffinityPreset will be ignored when it's set\n  ##\n  affinity: {}\n  ## @param metrics.nodeSelector Node labels for pod assignment\n  ## Ref: https://kubernetes.io/docs/user-guide/node-selection/\n  ##\n  nodeSelector: {}\n  ## @param metrics.tolerations Tolerations for pod assignment\n  ## Ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/\n  ##\n  tolerations: []\n  ## @param metrics.schedulerName Name of the k8s scheduler (other than default) for Airflow exporter\n  ## ref: https://kubernetes.io/docs/tasks/administer-cluster/configure-multiple-schedulers/\n  ##\n  schedulerName: \"\"\n  ## Airflow exporter service configuration\n  ##\n  service:\n    ## @param metrics.service.ports.http Airflow exporter metrics service port\n    ##\n    ports:\n      http: 9112\n    ## @param metrics.service.clusterIP Static clusterIP or None for headless services\n    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#choosing-your-own-ip-address\n    ##\n    clusterIP: \"\"\n    ## @param metrics.service.sessionAffinity Control where client requests go, to the same pod or round-robin\n    ## Values: ClientIP or None\n    ## ref: https://kubernetes.io/docs/user-guide/services/\n    ##\n    sessionAffinity: None\n    ## @param metrics.service.annotations [object] Annotations for the Airflow exporter service\n    ##\n    annotations:\n      prometheus.io/scrape: \"true\"\n      prometheus.io/port: \"{{ .Values.metrics.service.ports.http }}\"\n  ## Prometheus Operator ServiceMonitor configuration\n  ##\n  serviceMonitor:\n    ## @param metrics.serviceMonitor.enabled if `true`, creates a Prometheus Operator ServiceMonitor (requires `metrics.enabled` to be `true`)\n    ##\n    enabled: false\n    ## @param metrics.serviceMonitor.namespace Namespace in which Prometheus is running\n    ##\n    namespace: \"\"\n    ## @param metrics.serviceMonitor.interval Interval at which metrics should be scraped\n    ## ref: https://github.com/coreos/prometheus-operator/blob/master/Documentation/api.md#endpoint\n    ##\n    interval: \"\"\n    ## @param metrics.serviceMonitor.scrapeTimeout Timeout after which the scrape is ended\n    ## ref: https://github.com/coreos/prometheus-operator/blob/master/Documentation/api.md#endpoint\n    ##\n    scrapeTimeout: \"\"\n    ## @param metrics.serviceMonitor.labels Additional labels that can be used so ServiceMonitor will be discovered by Prometheus\n    ##\n    labels: {}\n    ## @param metrics.serviceMonitor.selector Prometheus instance selector labels\n    ## ref: https://github.com/bitnami/charts/tree/main/bitnami/prometheus-operator#prometheus-configuration\n    ##\n    selector: {}\n    ## @param metrics.serviceMonitor.relabelings RelabelConfigs to apply to samples before scraping\n    ##\n    relabelings: []\n    ## @param metrics.serviceMonitor.metricRelabelings MetricRelabelConfigs to apply to samples before ingestion\n    ##\n    metricRelabelings: []\n    ## @param metrics.serviceMonitor.honorLabels Specify honorLabels parameter to add the scrape endpoint\n    ##\n    honorLabels: false\n    ## @param metrics.serviceMonitor.jobLabel The name of the label on the target service to use as the job name in prometheus.\n    ##\n    jobLabel: \"\"\n\n## @section Airflow database parameters\n\n## PostgreSQL chart configuration\n## ref: https://github.com/bitnami/charts/blob/main/bitnami/postgresql/values.yaml\n## @param postgresql.enabled Switch to enable or disable the PostgreSQL helm chart\n## @param postgresql.auth.enablePostgresUser Assign a password to the \"postgres\" admin user. Otherwise, remote access will be blocked for this user\n## @param postgresql.auth.username Name for a custom user to create\n## @param postgresql.auth.password Password for the custom user to create\n## @param postgresql.auth.database Name for a custom database to create\n## @param postgresql.auth.existingSecret Name of existing secret to use for PostgreSQL credentials\n## @param postgresql.architecture PostgreSQL architecture (`standalone` or `replication`)\n##\npostgresql:\n  enabled: true\n  auth:\n    enablePostgresUser: false\n    username: bn_airflow\n    password: \"\"\n    database: bitnami_airflow\n    existingSecret: \"\"\n  architecture: standalone\n## External PostgreSQL configuration\n## All of these values are only used when postgresql.enabled is set to false\n## @param externalDatabase.host Database host\n## @param externalDatabase.port Database port number\n## @param externalDatabase.user Non-root username for Airflow\n## @param externalDatabase.password Password for the non-root username for Airflow\n## @param externalDatabase.database Airflow database name\n## @param externalDatabase.existingSecret Name of an existing secret resource containing the database credentials\n## @param externalDatabase.existingSecretPasswordKey Name of an existing secret key containing the database credentials\n##\nexternalDatabase:\n  host: localhost\n  port: 5432\n  user: bn_airflow\n  database: bitnami_airflow\n  password: \"\"\n  existingSecret: \"\"\n  existingSecretPasswordKey: \"\"\n\n## Redis&reg; chart configuration\n## ref: https://github.com/bitnami/charts/blob/main/bitnami/redis/values.yaml\n## @param redis.enabled Switch to enable or disable the Redis&reg; helm\n## @param redis.auth.enabled Enable password authentication\n## @param redis.auth.password Redis&reg; password\n## @param redis.auth.existingSecret The name of an existing secret with Redis&reg; credentials\n## @param redis.architecture Redis&reg; architecture. Allowed values: `standalone` or `replication`\n##\nredis:\n  enabled: true\n  auth:\n    enabled: true\n    ## Redis&reg; password (both master and slave). Defaults to a random 10-character alphanumeric string if not set and auth.enabled is true.\n    ## It should always be set using the password value or in the existingSecret to avoid issues\n    ## with Airflow.\n    ## The password value is ignored if existingSecret is set\n    password: \"\"\n    existingSecret: \"\"\n  architecture: standalone\n\n## External Redis&reg; configuration\n## All of these values are only used when redis.enabled is set to false\n## @param externalRedis.host Redis&reg; host\n## @param externalRedis.port Redis&reg; port number\n## @param externalRedis.username Redis&reg; username\n## @param externalRedis.password Redis&reg; password\n## @param externalRedis.existingSecret Name of an existing secret resource containing the Redis&trade credentials\n## @param externalRedis.existingSecretPasswordKey Name of an existing secret key containing the Redis&trade credentials\n##\nexternalRedis:\n  host: localhost\n  port: 6379\n  ## Most Redis&reg; implementations do not require a username\n  ## to authenticate and it should be enough with the password\n  username: \"\"\n  password: \"\"\n  existingSecret: \"\"\n  existingSecretPasswordKey: \"\"\n",
			Readme:                    "",
			UserId:                    6,
			ReferenceValueId:          6304,
			ReferenceValueKind:        "DEFAULT",
			ACDAppName:                "",
			Environment:               nil,
			ChartGroupEntryId:         0,
			Status:                    appStoreBean.WF_UNKNOWN,
			AppStoreId:                0,
			AppStoreName:              "",
			Deprecated:                false,
			ForceDelete:               false,
			ClusterId:                 0,
			Namespace:                 "devtron-demo",
			AppOfferingMode:           "",
			GitOpsRepoURL:             "",
			GitOpsPath:                "",
			GitHash:                   "",
			EnvironmentName:           "",
			InstallAppVersionChartDTO: nil,
			DeploymentAppType:         "helm",
		}

		installedAppVersion, err := AppStoreDeploymentService.AppStoreDeployOperationDB(&InstallAppVersionDTO, tx, appStoreBean.INSTALL_APP_REQUEST)

		assert.Nil(t, err)
		assert.Equal(t, installedAppVersion.DeploymentAppType, "helm")

	})

	t.Run("AppStoreDeployOperationDBExternallUse", func(t *testing.T) {
		AppStoreDeploymentService := initAppStoreDeploymentService(t, false)

		sugaredLogger, _ := util.InitLogger()
		config, _ := sql.GetConfig()
		db, _ := sql.NewDbConnection(config, sugaredLogger)

		tx, _ := db.Begin()

		InstallAppVersionDTO := appStoreBean.InstallAppVersionDTO{
			Id:                        0,
			AppId:                     0,
			AppName:                   "samplesamplesample",
			TeamId:                    1,
			EnvironmentId:             1,
			InstalledAppId:            0,
			InstalledAppVersionId:     0,
			AppStoreVersion:           6304,
			ValuesOverrideYaml:        "## @section Global parameters\n## Global Docker image parameters\n## Please, note that this will override the image parameters, including dependencies, configured to use the global value\n## Current available global Docker image parameters: imageRegistry, imagePullSecrets and storageClass\n\n## @param global.imageRegistry Global Docker image registry\n## @param global.imagePullSecrets Global Docker registry secret names as an array\n## @param global.storageClass Global StorageClass for Persistent Volume(s)\n##\nglobal:\n  imageRegistry: \"\"\n  ## E.g.\n  ## imagePullSecrets:\n  ##   - myRegistryKeySecretName\n  ##\n  imagePullSecrets: []\n  storageClass: \"\"\n\n## @section Common parameters\n\n## @param kubeVersion Override Kubernetes version\n##\nkubeVersion: \"\"\n## @param nameOverride String to partially override common.names.fullname template (will maintain the release name)\n##\nnameOverride: \"\"\n## @param fullnameOverride String to fully override common.names.fullname template\n##\nfullnameOverride: \"\"\n## @param clusterDomain Kubernetes Cluster Domain\n##\nclusterDomain: cluster.local\n## @param extraDeploy Extra objects to deploy (evaluated as a template)\n##\nextraDeploy: []\n## @param commonLabels Add labels to all the deployed resources\n##\ncommonLabels: {}\n## @param commonAnnotations Add annotations to all the deployed resources\n##\ncommonAnnotations: {}\n## Enable diagnostic mode in the deployment(s)/statefulset(s)\n##\ndiagnosticMode:\n  ## @param diagnosticMode.enabled Enable diagnostic mode (all probes will be disabled and the command will be overridden)\n  ##\n  enabled: false\n  ## @param diagnosticMode.command Command to override all containers in the the deployment(s)/statefulset(s)\n  ##\n  command:\n    - sleep\n  ## @param diagnosticMode.args Args to override all containers in the the deployment(s)/statefulset(s)\n  ##\n  args:\n    - infinity\n\n## @section Airflow common parameters\n\n## Authentication parameters\n## ref: https://github.com/bitnami/containers/tree/main/bitnami/airflow#environment-variables\n##\nauth:\n  ## @param auth.username Username to access web UI\n  ##\n  username: user\n  ## @param auth.password Password to access web UI\n  ##\n  password: \"\"\n  ## @param auth.fernetKey Fernet key to secure connections\n  ## ref: https://airflow.readthedocs.io/en/stable/howto/secure-connections.html\n  ## ref: https://bcb.github.io/airflow/fernet-key\n  ##\n  fernetKey: \"\"\n  ## @param auth.secretKey Secret key to run your flask app\n  ## ref: https://airflow.apache.org/docs/apache-airflow/stable/configurations-ref.html#secret-key\n  ##\n  secretKey: \"\"\n  ## @param auth.existingSecret Name of an existing secret to use for Airflow credentials\n  ## `auth.password`, `auth.fernetKey`, and `auth.secretKey` will be ignored and picked up from this secret\n  ## The secret must contain the keys `airflow-password`, `airflow-fernet-key` and `airflow-secret-key'\n  ## The value is evaluated as a template\n  ##\n  existingSecret: \"\"\n## @param executor Airflow executor. Allowed values: `SequentialExecutor`, `LocalExecutor`, `CeleryExecutor`, `KubernetesExecutor`, `CeleryKubernetesExecutor` and `LocalKubernetesExecutor`\n## ref: http://airflow.apache.org/docs/stable/executor/index.html\n##\nexecutor: CeleryExecutor\n## @param loadExamples Switch to load some Airflow examples\n##\nloadExamples: false\n## @param configuration Specify content for Airflow config file (auto-generated based on other env. vars otherwise)\n## e.g:\n## configuration: |-\n##   [core]\n##   dags_folder=/opt/bitnami/airflow/dags\n##   ...\n##\nconfiguration: \"\"\n## @param existingConfigmap Name of an existing ConfigMap with the Airflow config file\n##\nexistingConfigmap: \"\"\n## Load custom DAGs from a ConfigMap\n## Note: an init container will be used to prepare the DAGs available in the ConfigMap to be consumed by Airflow containers\n##\ndags:\n  ## @param dags.existingConfigmap Name of an existing ConfigMap with all the DAGs files you want to load in Airflow\n  ##\n  existingConfigmap: \"\"\n  ## Bitnami Shell image\n  ## ref: https://hub.docker.com/r/bitnami/bitnami-shell/tags/\n  ## @param dags.image.registry Init container load-dags image registry\n  ## @param dags.image.repository Init container load-dags image repository\n  ## @param dags.image.tag Init container load-dags image tag (immutable tags are recommended)\n  ## @param dags.image.digest Init container load-dags image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag\n  ## @param dags.image.pullPolicy Init container load-dags image pull policy\n  ## @param dags.image.pullSecrets Init container load-dags image pull secrets\n  ##\n  image:\n    registry: docker.io\n    repository: bitnami/bitnami-shell\n    tag: 11-debian-11-r50\n    digest: \"\"\n    pullPolicy: IfNotPresent\n    ## Optionally specify an array of imagePullSecrets.\n    ## Secrets must be manually created in the namespace.\n    ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/\n    ## e.g:\n    ## pullSecrets:\n    ##   - myRegistryKeySecretName\n    ##\n    pullSecrets: []\n## @param extraEnvVars Add extra environment variables for all the Airflow pods\n##\nextraEnvVars: []\n## @param extraEnvVarsCM ConfigMap with extra environment variables for all the Airflow pods\n##\nextraEnvVarsCM: \"\"\n## @param extraEnvVarsSecret Secret with extra environment variables for all the Airflow pods\n##\nextraEnvVarsSecret: \"\"\n## @param extraEnvVarsSecrets List of secrets with extra environment variables for all the Airflow pods\n##\nextraEnvVarsSecrets: []\n## @param sidecars Add additional sidecar containers to all the Airflow pods\n## Example:\n## sidecars:\n##   - name: your-image-name\n##     image: your-image\n##     imagePullPolicy: Always\n##     ports:\n##       - name: portname\n##         containerPort: 1234\n##\nsidecars: []\n## @param initContainers Add additional init containers to all the Airflow pods\n## Example:\n## initContainers:\n##   - name: your-image-name\n##     image: your-image\n##     imagePullPolicy: Always\n##     ports:\n##       - name: portname\n##         containerPort: 1234\n##\ninitContainers: []\n## @param extraVolumeMounts Optionally specify extra list of additional volumeMounts for all the Airflow pods\n##\nextraVolumeMounts: []\n## @param extraVolumes Optionally specify extra list of additional volumes for the all the Airflow pods\n##\nextraVolumes: []\n\n## @section Airflow web parameters\n\nweb:\n  ## Bitnami Airflow image version\n  ## ref: https://hub.docker.com/r/bitnami/airflow/tags/\n  ## @param web.image.registry Airflow image registry\n  ## @param web.image.repository Airflow image repository\n  ## @param web.image.tag Airflow image tag (immutable tags are recommended)\n  ## @param web.image.digest Airflow image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag\n  ## @param web.image.pullPolicy Airflow image pull policy\n  ## @param web.image.pullSecrets Airflow image pull secrets\n  ## @param web.image.debug Enable image debug mode\n  image:\n    registry: docker.io\n    repository: bitnami/airflow\n    tag: 2.4.2-debian-11-r6\n    digest: \"\"\n    ## Specify a imagePullPolicy\n    ## Defaults to 'Always' if image tag is 'latest', else set to 'IfNotPresent'\n    ## ref: https://kubernetes.io/docs/user-guide/images/#pre-pulling-images\n    ##\n    pullPolicy: IfNotPresent\n    ## Optionally specify an array of imagePullSecrets.\n    ## Secrets must be manually created in the namespace.\n    ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/\n    ## e.g:\n    ## pullSecrets:\n    ##   - myRegistryKeySecretName\n    ##\n    pullSecrets: []\n    ## Set to true if you would like to see extra information on logs\n    ##\n    debug: false\n  ## @param web.baseUrl URL used to access to Airflow web ui\n  ##\n  baseUrl: \"\"\n  ## @param web.existingConfigmap Name of an existing config map containing the Airflow web config file\n  ##\n  existingConfigmap: \"\"\n  ## @param web.command Override default container command (useful when using custom images)\n  ##\n  command: []\n  ## @param web.args Override default container args (useful when using custom images)\n  ##\n  args: []\n  ## @param web.extraEnvVars Array with extra environment variables to add Airflow web pods\n  ##\n  extraEnvVars: []\n  ## @param web.extraEnvVarsCM ConfigMap containing extra environment variables for Airflow web pods\n  ##\n  extraEnvVarsCM: \"\"\n  ## @param web.extraEnvVarsSecret Secret containing extra environment variables (in case of sensitive data) for Airflow web pods\n  ##\n  extraEnvVarsSecret: \"\"\n  ## @param web.extraEnvVarsSecrets List of secrets with extra environment variables for Airflow web pods\n  ##\n  extraEnvVarsSecrets: []\n  ## @param web.containerPorts.http Airflow web HTTP container port\n  ##\n  containerPorts:\n    http: 8080\n  ## @param web.replicaCount Number of Airflow web replicas\n  ##\n  replicaCount: 1\n  ## Configure extra options for Airflow web containers' liveness, readiness and startup probes\n  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/#configure-probes\n  ## @param web.livenessProbe.enabled Enable livenessProbe on Airflow web containers\n  ## @param web.livenessProbe.initialDelaySeconds Initial delay seconds for livenessProbe\n  ## @param web.livenessProbe.periodSeconds Period seconds for livenessProbe\n  ## @param web.livenessProbe.timeoutSeconds Timeout seconds for livenessProbe\n  ## @param web.livenessProbe.failureThreshold Failure threshold for livenessProbe\n  ## @param web.livenessProbe.successThreshold Success threshold for livenessProbe\n  ##\n  livenessProbe:\n    enabled: true\n    initialDelaySeconds: 180\n    periodSeconds: 20\n    timeoutSeconds: 5\n    failureThreshold: 6\n    successThreshold: 1\n  ## @param web.readinessProbe.enabled Enable readinessProbe on Airflow web containers\n  ## @param web.readinessProbe.initialDelaySeconds Initial delay seconds for readinessProbe\n  ## @param web.readinessProbe.periodSeconds Period seconds for readinessProbe\n  ## @param web.readinessProbe.timeoutSeconds Timeout seconds for readinessProbe\n  ## @param web.readinessProbe.failureThreshold Failure threshold for readinessProbe\n  ## @param web.readinessProbe.successThreshold Success threshold for readinessProbe\n  ##\n  readinessProbe:\n    enabled: true\n    initialDelaySeconds: 30\n    periodSeconds: 10\n    timeoutSeconds: 5\n    failureThreshold: 6\n    successThreshold: 1\n  ## @param web.startupProbe.enabled Enable startupProbe on Airflow web containers\n  ## @param web.startupProbe.initialDelaySeconds Initial delay seconds for startupProbe\n  ## @param web.startupProbe.periodSeconds Period seconds for startupProbe\n  ## @param web.startupProbe.timeoutSeconds Timeout seconds for startupProbe\n  ## @param web.startupProbe.failureThreshold Failure threshold for startupProbe\n  ## @param web.startupProbe.successThreshold Success threshold for startupProbe\n  ##\n  startupProbe:\n    enabled: false\n    initialDelaySeconds: 60\n    periodSeconds: 10\n    timeoutSeconds: 1\n    failureThreshold: 15\n    successThreshold: 1\n  ## @param web.customLivenessProbe Custom livenessProbe that overrides the default one\n  ##\n  customLivenessProbe: {}\n  ## @param web.customReadinessProbe Custom readinessProbe that overrides the default one\n  ##\n  customReadinessProbe: {}\n  ## @param web.customStartupProbe Custom startupProbe that overrides the default one\n  ##\n  customStartupProbe: {}\n  ## Airflow web resource requests and limits\n  ## ref: https://kubernetes.io/docs/user-guide/compute-resources/\n  ## @param web.resources.limits The resources limits for the Airflow web containers\n  ## @param web.resources.requests The requested resources for the Airflow web containers\n  ##\n  resources:\n    limits: {}\n    requests: {}\n  ## Configure Airflow web pods Security Context\n  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod\n  ## @param web.podSecurityContext.enabled Enabled Airflow web pods' Security Context\n  ## @param web.podSecurityContext.fsGroup Set Airflow web pod's Security Context fsGroup\n  ##\n  podSecurityContext:\n    enabled: true\n    fsGroup: 1001\n  ## Configure Airflow web containers (only main one) Security Context\n  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container\n  ## @param web.containerSecurityContext.enabled Enabled Airflow web containers' Security Context\n  ## @param web.containerSecurityContext.runAsUser Set Airflow web containers' Security Context runAsUser\n  ## @param web.containerSecurityContext.runAsNonRoot Set Airflow web containers' Security Context runAsNonRoot\n  ##\n  containerSecurityContext:\n    enabled: true\n    runAsUser: 1001\n    runAsNonRoot: true\n  ## @param web.lifecycleHooks for the Airflow web container(s) to automate configuration before or after startup\n  ##\n  lifecycleHooks: {}\n  ## @param web.hostAliases Deployment pod host aliases\n  ## https://kubernetes.io/docs/concepts/services-networking/add-entries-to-pod-etc-hosts-with-host-aliases/\n  ##\n  hostAliases: []\n  ## @param web.podLabels Add extra labels to the Airflow web pods\n  ##\n  podLabels: {}\n  ## @param web.podAnnotations Add extra annotations to the Airflow web pods\n  ##\n  podAnnotations: {}\n  ## @param web.affinity Affinity for Airflow web pods assignment (evaluated as a template)\n  ## Ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity\n  ## Note: `web.podAffinityPreset`, `web.podAntiAffinityPreset`, and `web.nodeAffinityPreset` will be ignored when it's set\n  ##\n  affinity: {}\n  ## Node affinity preset\n  ## Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#node-affinity\n  ## @param web.nodeAffinityPreset.key Node label key to match. Ignored if `web.affinity` is set.\n  ## @param web.nodeAffinityPreset.type Node affinity preset type. Ignored if `web.affinity` is set. Allowed values: `soft` or `hard`\n  ## @param web.nodeAffinityPreset.values Node label values to match. Ignored if `web.affinity` is set.\n  ##\n  nodeAffinityPreset:\n    ## e.g:\n    ## key: \"kubernetes.io/e2e-az-name\"\n    ##\n    key: \"\"\n    type: \"\"\n    ## e.g:\n    ## values:\n    ##   - e2e-az1\n    ##   - e2e-az2\n    ##\n    values: []\n  ## @param web.nodeSelector Node labels for Airflow web pods assignment\n  ## Ref: https://kubernetes.io/docs/user-guide/node-selection/\n  ##\n  nodeSelector: {}\n  ## @param web.podAffinityPreset Pod affinity preset. Ignored if `web.affinity` is set. Allowed values: `soft` or `hard`.\n  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity\n  ##\n  podAffinityPreset: \"\"\n  ## @param web.podAntiAffinityPreset Pod anti-affinity preset. Ignored if `web.affinity` is set. Allowed values: `soft` or `hard`.\n  ## Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity\n  ##\n  podAntiAffinityPreset: soft\n  ## @param web.tolerations Tolerations for Airflow web pods assignment\n  ## Ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/\n  ##\n  tolerations: []\n  ## @param web.topologySpreadConstraints Topology Spread Constraints for pod assignment spread across your cluster among failure-domains. Evaluated as a template\n  ## Ref: https://kubernetes.io/docs/concepts/workloads/pods/pod-topology-spread-constraints/#spread-constraints-for-pods\n  ##\n  topologySpreadConstraints: []\n  ## @param web.priorityClassName Priority Class Name\n  ## ref: https://kubernetes.io/docs/concepts/configuration/pod-priority-preemption/#priorityclass\n  ##\n  priorityClassName: \"\"\n  ## @param web.schedulerName Use an alternate scheduler, e.g. \"stork\".\n  ## ref: https://kubernetes.io/docs/tasks/administer-cluster/configure-multiple-schedulers/\n  ##\n  schedulerName: \"\"\n  ## @param web.terminationGracePeriodSeconds Seconds Airflow web pod needs to terminate gracefully\n  ## ref: https://kubernetes.io/docs/concepts/workloads/pods/pod/#termination-of-pods\n  ##\n  terminationGracePeriodSeconds: \"\"\n  ## @param web.updateStrategy.type Airflow web deployment strategy type\n  ## @param web.updateStrategy.rollingUpdate Airflow web deployment rolling update configuration parameters\n  ## ref: https://kubernetes.io/docs/concepts/workloads/controllers/deployment/#strategy\n  ##\n  updateStrategy:\n    type: RollingUpdate\n    rollingUpdate: {}\n  ## @param web.sidecars Add additional sidecar containers to the Airflow web pods\n  ## Example:\n  ## sidecars:\n  ##   - name: your-image-name\n  ##     image: your-image\n  ##     imagePullPolicy: Always\n  ##     ports:\n  ##       - name: portname\n  ##         containerPort: 1234\n  ##\n  sidecars: []\n  ## @param web.initContainers Add additional init containers to the Airflow web pods\n  ## Example:\n  ## initContainers:\n  ##   - name: your-image-name\n  ##     image: your-image\n  ##     imagePullPolicy: Always\n  ##     ports:\n  ##       - name: portname\n  ##         containerPort: 1234\n  ##\n  initContainers: []\n  ## @param web.extraVolumeMounts Optionally specify extra list of additional volumeMounts for the Airflow web pods\n  ##\n  extraVolumeMounts: []\n  ## @param web.extraVolumes Optionally specify extra list of additional volumes for the Airflow web pods\n  ##\n  extraVolumes: []\n  ## Airflow web Pod Disruption Budget\n  ## ref: https://kubernetes.io/docs/concepts/workloads/pods/disruptions/\n  ## @param web.pdb.create Deploy a pdb object for the Airflow web pods\n  ## @param web.pdb.minAvailable Maximum number/percentage of unavailable Airflow web replicas\n  ## @param web.pdb.maxUnavailable Maximum number/percentage of unavailable Airflow web replicas\n  ##\n  pdb:\n    create: false\n    minAvailable: 1\n    maxUnavailable: \"\"\n\n## @section Airflow scheduler parameters\n\nscheduler:\n  ## Bitnami Airflow Scheduler image version\n  ## ref: https://hub.docker.com/r/bitnami/airflow-scheduler/tags/\n  ## @param scheduler.image.registry Airflow Scheduler image registry\n  ## @param scheduler.image.repository Airflow Scheduler image repository\n  ## @param scheduler.image.tag Airflow Scheduler image tag (immutable tags are recommended)\n  ## @param scheduler.image.digest Airflow Schefuler image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag\n  ## @param scheduler.image.pullPolicy Airflow Scheduler image pull policy\n  ## @param scheduler.image.pullSecrets Airflow Scheduler image pull secrets\n  ## @param scheduler.image.debug Enable image debug mode\n  ##\n  image:\n    registry: docker.io\n    repository: bitnami/airflow-scheduler\n    tag: 2.4.2-debian-11-r4\n    digest: \"\"\n    ## Specify a imagePullPolicy\n    ## Defaults to 'Always' if image tag is 'latest', else set to 'IfNotPresent'\n    ## ref: https://kubernetes.io/docs/user-guide/images/#pre-pulling-images\n    ##\n    pullPolicy: IfNotPresent\n    ## Optionally specify an array of imagePullSecrets.\n    ## Secrets must be manually created in the namespace.\n    ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/\n    ## e.g:\n    ## pullSecrets:\n    ##   - myRegistryKeySecretName\n    ##\n    pullSecrets: []\n    ## Set to true if you would like to see extra information on logs\n    ##\n    debug: false\n  ## @param scheduler.replicaCount Number of scheduler replicas\n  ##\n  replicaCount: 1\n  ## @param scheduler.command Override cmd\n  ##\n  command: []\n  ## @param scheduler.args Override args\n  ##\n  args: []\n  ## @param scheduler.extraEnvVars Add extra environment variables\n  ##\n  extraEnvVars: []\n  ## @param scheduler.extraEnvVarsCM ConfigMap with extra environment variables\n  ##\n  extraEnvVarsCM: \"\"\n  ## @param scheduler.extraEnvVarsSecret Secret with extra environment variables\n  ##\n  extraEnvVarsSecret: \"\"\n  ## @param scheduler.extraEnvVarsSecrets List of secrets with extra environment variables for Airflow scheduler pods\n  ##\n  extraEnvVarsSecrets: []\n  ## @param scheduler.customLivenessProbe Custom livenessProbe that overrides the default one\n  ##\n  customLivenessProbe: {}\n  ## @param scheduler.customReadinessProbe Custom readinessProbe that overrides the default one\n  ##\n  customReadinessProbe: {}\n  ## @param scheduler.customStartupProbe Custom startupProbe that overrides the default one\n  ##\n  customStartupProbe: {}\n  ## Airflow scheduler resource requests and limits\n  ## ref: https://kubernetes.io/docs/user-guide/compute-resources/\n  ## @param scheduler.resources.limits The resources limits for the Airflow scheduler containers\n  ## @param scheduler.resources.requests The requested resources for the Airflow scheduler containers\n  ##\n  resources:\n    limits: {}\n    requests: {}\n  ## Configure Airflow scheduler pods Security Context\n  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod\n  ## @param scheduler.podSecurityContext.enabled Enabled Airflow scheduler pods' Security Context\n  ## @param scheduler.podSecurityContext.fsGroup Set Airflow scheduler pod's Security Context fsGroup\n  ##\n  podSecurityContext:\n    enabled: true\n    fsGroup: 1001\n  ## Configure Airflow scheduler containers (only main one) Security Context\n  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container\n  ## @param scheduler.containerSecurityContext.enabled Enabled Airflow scheduler containers' Security Context\n  ## @param scheduler.containerSecurityContext.runAsUser Set Airflow scheduler containers' Security Context runAsUser\n  ## @param scheduler.containerSecurityContext.runAsNonRoot Set Airflow scheduler containers' Security Context runAsNonRoot\n  ##\n  containerSecurityContext:\n    enabled: true\n    runAsUser: 1001\n    runAsNonRoot: true\n  ## @param scheduler.lifecycleHooks for the Airflow scheduler container(s) to automate configuration before or after startup\n  ##\n  lifecycleHooks: {}\n  ## @param scheduler.hostAliases Deployment pod host aliases\n  ## https://kubernetes.io/docs/concepts/services-networking/add-entries-to-pod-etc-hosts-with-host-aliases/\n  ##\n  hostAliases: []\n  ## @param scheduler.podLabels Add extra labels to the Airflow scheduler pods\n  ##\n  podLabels: {}\n  ## @param scheduler.podAnnotations Add extra annotations to the Airflow scheduler pods\n  ##\n  podAnnotations: {}\n  ## @param scheduler.affinity Affinity for Airflow scheduler pods assignment (evaluated as a template)\n  ## Ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity\n  ## Note: `scheduler.podAffinityPreset`, `scheduler.podAntiAffinityPreset`, and `scheduler.nodeAffinityPreset` will be ignored when it's set\n  ##\n  affinity: {}\n  ## Node affinity preset\n  ## Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#node-affinity\n  ## @param scheduler.nodeAffinityPreset.key Node label key to match. Ignored if `scheduler.affinity` is set.\n  ## @param scheduler.nodeAffinityPreset.type Node affinity preset type. Ignored if `scheduler.affinity` is set. Allowed values: `soft` or `hard`\n  ## @param scheduler.nodeAffinityPreset.values Node label values to match. Ignored if `scheduler.affinity` is set.\n  ##\n  nodeAffinityPreset:\n    ## e.g:\n    ## key: \"kubernetes.io/e2e-az-name\"\n    ##\n    key: \"\"\n    type: \"\"\n    ## e.g:\n    ## values:\n    ##   - e2e-az1\n    ##   - e2e-az2\n    ##\n    values: []\n  ## @param scheduler.nodeSelector Node labels for Airflow scheduler pods assignment\n  ## Ref: https://kubernetes.io/docs/user-guide/node-selection/\n  ##\n  nodeSelector: {}\n  ## @param scheduler.podAffinityPreset Pod affinity preset. Ignored if `scheduler.affinity` is set. Allowed values: `soft` or `hard`.\n  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity\n  ##\n  podAffinityPreset: \"\"\n  ## @param scheduler.podAntiAffinityPreset Pod anti-affinity preset. Ignored if `scheduler.affinity` is set. Allowed values: `soft` or `hard`.\n  ## Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity\n  ##\n  podAntiAffinityPreset: soft\n  ## @param scheduler.tolerations Tolerations for Airflow scheduler pods assignment\n  ## Ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/\n  ##\n  tolerations: []\n  ## @param scheduler.topologySpreadConstraints Topology Spread Constraints for pod assignment spread across your cluster among failure-domains. Evaluated as a template\n  ## Ref: https://kubernetes.io/docs/concepts/workloads/pods/pod-topology-spread-constraints/#spread-constraints-for-pods\n  ##\n  topologySpreadConstraints: []\n  ## @param scheduler.priorityClassName Priority Class Name\n  ## ref: https://kubernetes.io/docs/concepts/configuration/pod-priority-preemption/#priorityclass\n  ##\n  priorityClassName: \"\"\n  ## @param scheduler.schedulerName Use an alternate scheduler, e.g. \"stork\".\n  ## ref: https://kubernetes.io/docs/tasks/administer-cluster/configure-multiple-schedulers/\n  ##\n  schedulerName: \"\"\n  ## @param scheduler.terminationGracePeriodSeconds Seconds Airflow scheduler pod needs to terminate gracefully\n  ## ref: https://kubernetes.io/docs/concepts/workloads/pods/pod/#termination-of-pods\n  ##\n  terminationGracePeriodSeconds: \"\"\n  ## @param scheduler.updateStrategy.type Airflow scheduler deployment strategy type\n  ## @param scheduler.updateStrategy.rollingUpdate Airflow scheduler deployment rolling update configuration parameters\n  ## ref: https://kubernetes.io/docs/concepts/workloads/controllers/deployment/#strategy\n  ##\n  updateStrategy:\n    type: RollingUpdate\n    rollingUpdate: {}\n  ## @param scheduler.sidecars Add additional sidecar containers to the Airflow scheduler pods\n  ## Example:\n  ## sidecars:\n  ##   - name: your-image-name\n  ##     image: your-image\n  ##     imagePullPolicy: Always\n  ##     ports:\n  ##       - name: portname\n  ##         containerPort: 1234\n  ##\n  sidecars: []\n  ## @param scheduler.initContainers Add additional init containers to the Airflow scheduler pods\n  ## Example:\n  ## initContainers:\n  ##   - name: your-image-name\n  ##     image: your-image\n  ##     imagePullPolicy: Always\n  ##     ports:\n  ##       - name: portname\n  ##         containerPort: 1234\n  ##\n  initContainers: []\n  ## @param scheduler.extraVolumeMounts Optionally specify extra list of additional volumeMounts for the Airflow scheduler pods\n  ##\n  extraVolumeMounts: []\n  ## @param scheduler.extraVolumes Optionally specify extra list of additional volumes for the Airflow scheduler pods\n  ##\n  extraVolumes: []\n  ## Airflow scheduler Pod Disruption Budget\n  ## ref: https://kubernetes.io/docs/concepts/workloads/pods/disruptions/\n  ## @param scheduler.pdb.create Deploy a pdb object for the Airflow scheduler pods\n  ## @param scheduler.pdb.minAvailable Maximum number/percentage of unavailable Airflow scheduler replicas\n  ## @param scheduler.pdb.maxUnavailable Maximum number/percentage of unavailable Airflow scheduler replicas\n  ##\n  pdb:\n    create: false\n    minAvailable: 1\n    maxUnavailable: \"\"\n\n## @section Airflow worker parameters\n\nworker:\n  ## Bitnami Airflow Worker image version\n  ## ref: https://hub.docker.com/r/bitnami/airflow-worker/tags/\n  ## @param worker.image.registry Airflow Worker image registry\n  ## @param worker.image.repository Airflow Worker image repository\n  ## @param worker.image.tag Airflow Worker image tag (immutable tags are recommended)\n  ## @param worker.image.digest Airflow Worker image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag\n  ## @param worker.image.pullPolicy Airflow Worker image pull policy\n  ## @param worker.image.pullSecrets Airflow Worker image pull secrets\n  ## @param worker.image.debug Enable image debug mode\n  ##\n  image:\n    registry: docker.io\n    repository: bitnami/airflow-worker\n    tag: 2.4.2-debian-11-r4\n    digest: \"\"\n    ## Specify a imagePullPolicy\n    ## Defaults to 'Always' if image tag is 'latest', else set to 'IfNotPresent'\n    ## ref: https://kubernetes.io/docs/user-guide/images/#pre-pulling-images\n    ##\n    pullPolicy: IfNotPresent\n    ## Optionally specify an array of imagePullSecrets.\n    ## Secrets must be manually created in the namespace.\n    ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/\n    ## e.g:\n    ## pullSecrets:\n    ##   - myRegistryKeySecretName\n    ##\n    pullSecrets: []\n    ## Set to true if you would like to see extra information on logs\n    ##\n    debug: false\n  ## @param worker.command Override default container command (useful when using custom images)\n  ##\n  command: []\n  ## @param worker.args Override default container args (useful when using custom images)\n  ##\n  args: []\n  ## @param worker.extraEnvVars Array with extra environment variables to add Airflow worker pods\n  ##\n  extraEnvVars: []\n  ## @param worker.extraEnvVarsCM ConfigMap containing extra environment variables for Airflow worker pods\n  ##\n  extraEnvVarsCM: \"\"\n  ## @param worker.extraEnvVarsSecret Secret containing extra environment variables (in case of sensitive data) for Airflow worker pods\n  ##\n  extraEnvVarsSecret: \"\"\n  ## @param worker.extraEnvVarsSecrets List of secrets with extra environment variables for Airflow worker pods\n  ##\n  extraEnvVarsSecrets: []\n  ## @param worker.containerPorts.http Airflow worker HTTP container port\n  ##\n  containerPorts:\n    http: 8793\n  ## @param worker.replicaCount Number of Airflow worker replicas\n  ##\n  replicaCount: 1\n  ## Configure extra options for Airflow worker containers' liveness, readiness and startup probes\n  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/#configure-probes\n  ## @param worker.livenessProbe.enabled Enable livenessProbe on Airflow worker containers\n  ## @param worker.livenessProbe.initialDelaySeconds Initial delay seconds for livenessProbe\n  ## @param worker.livenessProbe.periodSeconds Period seconds for livenessProbe\n  ## @param worker.livenessProbe.timeoutSeconds Timeout seconds for livenessProbe\n  ## @param worker.livenessProbe.failureThreshold Failure threshold for livenessProbe\n  ## @param worker.livenessProbe.successThreshold Success threshold for livenessProbe\n  ##\n  livenessProbe:\n    enabled: true\n    initialDelaySeconds: 180\n    periodSeconds: 20\n    timeoutSeconds: 5\n    failureThreshold: 6\n    successThreshold: 1\n  ## @param worker.readinessProbe.enabled Enable readinessProbe on Airflow worker containers\n  ## @param worker.readinessProbe.initialDelaySeconds Initial delay seconds for readinessProbe\n  ## @param worker.readinessProbe.periodSeconds Period seconds for readinessProbe\n  ## @param worker.readinessProbe.timeoutSeconds Timeout seconds for readinessProbe\n  ## @param worker.readinessProbe.failureThreshold Failure threshold for readinessProbe\n  ## @param worker.readinessProbe.successThreshold Success threshold for readinessProbe\n  ##\n  readinessProbe:\n    enabled: true\n    initialDelaySeconds: 30\n    periodSeconds: 10\n    timeoutSeconds: 5\n    failureThreshold: 6\n    successThreshold: 1\n  ## @param worker.startupProbe.enabled Enable startupProbe on Airflow worker containers\n  ## @param worker.startupProbe.initialDelaySeconds Initial delay seconds for startupProbe\n  ## @param worker.startupProbe.periodSeconds Period seconds for startupProbe\n  ## @param worker.startupProbe.timeoutSeconds Timeout seconds for startupProbe\n  ## @param worker.startupProbe.failureThreshold Failure threshold for startupProbe\n  ## @param worker.startupProbe.successThreshold Success threshold for startupProbe\n  ##\n  startupProbe:\n    enabled: false\n    initialDelaySeconds: 60\n    periodSeconds: 10\n    timeoutSeconds: 1\n    failureThreshold: 15\n    successThreshold: 1\n  ## @param worker.customLivenessProbe Custom livenessProbe that overrides the default one\n  ##\n  customLivenessProbe: {}\n  ## @param worker.customReadinessProbe Custom readinessProbe that overrides the default one\n  ##\n  customReadinessProbe: {}\n  ## @param worker.customStartupProbe Custom startupProbe that overrides the default one\n  ##\n  customStartupProbe: {}\n  ## Airflow worker resource requests and limits\n  ## ref: https://kubernetes.io/docs/user-guide/compute-resources/\n  ## @param worker.resources.limits The resources limits for the Airflow worker containers\n  ## @param worker.resources.requests The requested resources for the Airflow worker containers\n  ##\n  resources:\n    limits: {}\n    requests: {}\n  ## Configure Airflow worker pods Security Context\n  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod\n  ## @param worker.podSecurityContext.enabled Enabled Airflow worker pods' Security Context\n  ## @param worker.podSecurityContext.fsGroup Set Airflow worker pod's Security Context fsGroup\n  ##\n  podSecurityContext:\n    enabled: true\n    fsGroup: 1001\n  ## Configure Airflow worker containers (only main one) Security Context\n  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container\n  ## @param worker.containerSecurityContext.enabled Enabled Airflow worker containers' Security Context\n  ## @param worker.containerSecurityContext.runAsUser Set Airflow worker containers' Security Context runAsUser\n  ## @param worker.containerSecurityContext.runAsNonRoot Set Airflow worker containers' Security Context runAsNonRoot\n  ##\n  containerSecurityContext:\n    enabled: true\n    runAsUser: 1001\n    runAsNonRoot: true\n  ## @param worker.lifecycleHooks for the Airflow worker container(s) to automate configuration before or after startup\n  ##\n  lifecycleHooks: {}\n  ## @param worker.hostAliases Deployment pod host aliases\n  ## https://kubernetes.io/docs/concepts/services-networking/add-entries-to-pod-etc-hosts-with-host-aliases/\n  ##\n  hostAliases: []\n  ## @param worker.podLabels Add extra labels to the Airflow worker pods\n  ##\n  podLabels: {}\n  ## @param worker.podAnnotations Add extra annotations to the Airflow worker pods\n  ##\n  podAnnotations: {}\n  ## @param worker.affinity Affinity for Airflow worker pods assignment (evaluated as a template)\n  ## Ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity\n  ## Note: `worker.podAffinityPreset`, `worker.podAntiAffinityPreset`, and `worker.nodeAffinityPreset` will be ignored when it's set\n  ##\n  affinity: {}\n  ## Node affinity preset\n  ## Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#node-affinity\n  ## @param worker.nodeAffinityPreset.key Node label key to match. Ignored if `worker.affinity` is set.\n  ## @param worker.nodeAffinityPreset.type Node affinity preset type. Ignored if `worker.affinity` is set. Allowed values: `soft` or `hard`\n  ## @param worker.nodeAffinityPreset.values Node label values to match. Ignored if `worker.affinity` is set.\n  ##\n  nodeAffinityPreset:\n    ## e.g:\n    ## key: \"kubernetes.io/e2e-az-name\"\n    ##\n    key: \"\"\n    type: \"\"\n    ## e.g:\n    ## values:\n    ##   - e2e-az1\n    ##   - e2e-az2\n    ##\n    values: []\n  ## @param worker.nodeSelector Node labels for Airflow worker pods assignment\n  ## Ref: https://kubernetes.io/docs/user-guide/node-selection/\n  ##\n  nodeSelector: {}\n  ## @param worker.podAffinityPreset Pod affinity preset. Ignored if `worker.affinity` is set. Allowed values: `soft` or `hard`.\n  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity\n  ##\n  podAffinityPreset: \"\"\n  ## @param worker.podAntiAffinityPreset Pod anti-affinity preset. Ignored if `worker.affinity` is set. Allowed values: `soft` or `hard`.\n  ## Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity\n  ##\n  podAntiAffinityPreset: soft\n  ## @param worker.tolerations Tolerations for Airflow worker pods assignment\n  ## Ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/\n  ##\n  tolerations: []\n  ## @param worker.topologySpreadConstraints Topology Spread Constraints for pod assignment spread across your cluster among failure-domains. Evaluated as a template\n  ## Ref: https://kubernetes.io/docs/concepts/workloads/pods/pod-topology-spread-constraints/#spread-constraints-for-pods\n  ##\n  topologySpreadConstraints: []\n  ## @param worker.priorityClassName Priority Class Name\n  ## ref: https://kubernetes.io/docs/concepts/configuration/pod-priority-preemption/#priorityclass\n  ##\n  priorityClassName: \"\"\n  ## @param worker.schedulerName Use an alternate scheduler, e.g. \"stork\".\n  ## ref: https://kubernetes.io/docs/tasks/administer-cluster/configure-multiple-schedulers/\n  ##\n  schedulerName: \"\"\n  ## @param worker.terminationGracePeriodSeconds Seconds Airflow worker pod needs to terminate gracefully\n  ## ref: https://kubernetes.io/docs/concepts/workloads/pods/pod/#termination-of-pods\n  ##\n  terminationGracePeriodSeconds: \"\"\n  ## @param worker.updateStrategy.type Airflow worker deployment strategy type\n  ## @param worker.updateStrategy.rollingUpdate Airflow worker deployment rolling update configuration parameters\n  ## ref: https://kubernetes.io/docs/concepts/workloads/controllers/deployment/#strategy\n  ##\n  updateStrategy:\n    type: RollingUpdate\n    rollingUpdate: {}\n  ## @param worker.sidecars Add additional sidecar containers to the Airflow worker pods\n  ## Example:\n  ## sidecars:\n  ##   - name: your-image-name\n  ##     image: your-image\n  ##     imagePullPolicy: Always\n  ##     ports:\n  ##       - name: portname\n  ##         containerPort: 1234\n  ##\n  sidecars: []\n  ## @param worker.initContainers Add additional init containers to the Airflow worker pods\n  ## Example:\n  ## initContainers:\n  ##   - name: your-image-name\n  ##     image: your-image\n  ##     imagePullPolicy: Always\n  ##     ports:\n  ##       - name: portname\n  ##         containerPort: 1234\n  ##\n  initContainers: []\n  ## @param worker.extraVolumeMounts Optionally specify extra list of additional volumeMounts for the Airflow worker pods\n  ##\n  extraVolumeMounts: []\n  ## @param worker.extraVolumes Optionally specify extra list of additional volumes for the Airflow worker pods\n  ##\n  extraVolumes: []\n  ## @param worker.extraVolumeClaimTemplates Optionally specify extra list of volumesClaimTemplates for the Airflow worker statefulset\n  ##\n  extraVolumeClaimTemplates: []\n  ## @param worker.podTemplate Template to replace the default one to be use when `executor=KubernetesExecutor` to create Airflow worker pods\n  ##\n  podTemplate: {}\n  ## Airflow worker Pod Disruption Budget\n  ## ref: https://kubernetes.io/docs/concepts/workloads/pods/disruptions/\n  ## @param worker.pdb.create Deploy a pdb object for the Airflow worker pods\n  ## @param worker.pdb.minAvailable Maximum number/percentage of unavailable Airflow worker replicas\n  ## @param worker.pdb.maxUnavailable Maximum number/percentage of unavailable Airflow worker replicas\n  ##\n  pdb:\n    create: false\n    minAvailable: 1\n    maxUnavailable: \"\"\n  ## Enable HorizontalPodAutoscaler for worker pods\n  ## ref: https://kubernetes.io/docs/tasks/run-application/horizontal-pod-autoscale/\n  ## @param worker.autoscaling.enabled Whether enable horizontal pod autoscaler\n  ## @param worker.autoscaling.minReplicas Configure a minimum amount of pods\n  ## @param worker.autoscaling.maxReplicas Configure a maximum amount of pods\n  ## @param worker.autoscaling.targetCPU Define the CPU target to trigger the scaling actions (utilization percentage)\n  ## @param worker.autoscaling.targetMemory Define the memory target to trigger the scaling actions (utilization percentage)\n  ##\n  autoscaling:\n    enabled: false\n    minReplicas: 1\n    maxReplicas: 3\n    targetCPU: 80\n    targetMemory: 80\n\n## @section Airflow git sync parameters\n\n## Configure Git to pull dags and plugins\n##\ngit:\n  ## Bitnami Git image version\n  ## ref: https://hub.docker.com/r/bitnami/git/tags/\n  ## @param git.image.registry Git image registry\n  ## @param git.image.repository Git image repository\n  ## @param git.image.tag Git image tag (immutable tags are recommended)\n  ## @param git.image.digest Git image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag\n  ## @param git.image.pullPolicy Git image pull policy\n  ## @param git.image.pullSecrets Git image pull secrets\n  ##\n  image:\n    registry: docker.io\n    repository: bitnami/git\n    tag: 2.38.1-debian-11-r7\n    digest: \"\"\n    ## Specify a imagePullPolicy\n    ## Defaults to 'Always' if image tag is 'latest', else set to 'IfNotPresent'\n    ## ref: https://kubernetes.io/docs/user-guide/images/#pre-pulling-images\n    ##\n    pullPolicy: IfNotPresent\n    ## Optionally specify an array of imagePullSecrets.\n    ## Secrets must be manually created in the namespace.\n    ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/\n    ## e.g:\n    ## pullSecrets:\n    ##   - myRegistryKeySecretName\n    ##\n    pullSecrets: []\n  ## Get DAG files from git repositories\n  ## @param git.dags.enabled Enable in order to download DAG files from git repositories.\n  ## @param git.dags.repositories [array] Array of repositories from which to download DAG files\n  ##\n  dags:\n    enabled: false\n    ## Name for repositories can be anything unique and must follow same naming conventions as kubernetes.\n    ## Kubernetes resources can have names up to 253 characters long. The characters allowed in names are:\n    ## digits (0-9), lower case letters (a-z), -, and .\n    ## Example:\n    ##   - repository: https://github.com/myuser/myrepo\n    ##     branch: main\n    ##     name: my-dags\n    ##     path: /\n    ##\n    repositories:\n      - repository: \"\"\n        ## Branch from repository to checkout\n        ##\n        branch: \"\"\n        ## An unique identifier for repository, must be unique for each repository\n        ##\n        name: \"\"\n        ## Path to a folder in the repository containing the dags\n        ##\n        path: \"\"\n  ## Get Plugins files from git repositories.\n  ## @param git.plugins.enabled Enable in order to download Plugins files from git repositories.\n  ## @param git.plugins.repositories [array] Array of repositories from which to download DAG files\n  ##\n  plugins:\n    enabled: false\n    repositories:\n      - repository: \"\"\n        ## Branch from repository to checkout\n        ##\n        branch: \"\"\n        ## An unique identifier for repository, must be unique for each repository\n        ##\n        name: \"\"\n        ## Path to a folder in the repository containing the plugins\n        ##\n        path: \"\"\n  ## Properties for the Clone init container\n  ## @param git.clone.command Override cmd\n  ## @param git.clone.args Override args\n  ## @param git.clone.extraVolumeMounts Add extra volume mounts\n  ## @param git.clone.extraEnvVars Add extra environment variables\n  ## @param git.clone.extraEnvVarsCM ConfigMap with extra environment variables\n  ## @param git.clone.extraEnvVarsSecret Secret with extra environment variables\n  ## @param git.clone.resources Clone init container resource requests and limits\n  ##\n  clone:\n    command: []\n    args: []\n    extraVolumeMounts: []\n    extraEnvVars: []\n    extraEnvVarsCM: \"\"\n    extraEnvVarsSecret: \"\"\n    ## Clone init container resource requests and limits\n    ## ref: https://kubernetes.io/docs/user-guide/compute-resources/\n    ##\n    resources: {}\n  ## Properties for the Sync sidecar container\n  ## @param git.sync.interval Interval in seconds to pull the git repository containing the plugins and/or DAG files\n  ## @param git.sync.command Override cmd\n  ## @param git.sync.args Override args\n  ## @param git.sync.extraVolumeMounts Add extra volume mounts\n  ## @param git.sync.extraEnvVars Add extra environment variables\n  ## @param git.sync.extraEnvVarsCM ConfigMap with extra environment variables\n  ## @param git.sync.extraEnvVarsSecret Secret with extra environment variables\n  ## @param git.sync.resources Sync sidecar container resource requests and limits\n  ##\n  sync:\n    interval: 60\n    command: []\n    args: []\n    extraVolumeMounts: []\n    extraEnvVars: []\n    extraEnvVarsCM: \"\"\n    extraEnvVarsSecret: \"\"\n    ## Sync sidecar container resource requests and limits\n    ## ref: https://kubernetes.io/docs/user-guide/compute-resources/\n    ##\n    resources: {}\n\n## @section Airflow ldap parameters\n\n## LDAP configuration\n## @param ldap.enabled Enable LDAP authentication\n## @param ldap.uri Server URI, eg. ldap://ldap_server:389\n## DEPRECATED ldap.base It will be removed in a future release, please use 'ldap.basedn' instead\n## @param ldap.basedn Base of the search, eg. ou=example,o=org.\n## DEPRECATED ldap.uidField It will be removed in a future release,, please use 'ldap.searchAttribute' instead\n## @param ldap.searchAttribute if doing an indirect bind to ldap, this is the field that matches the username when searching for the account to bind to\n## @param ldap.binddn DN of the account used to search in the LDAP server.\n## @param ldap.bindpw Bind Password\n## @param ldap.userRegistration Set to True to enable user self registration\n## @param ldap.userRegistrationRole Set role name to be assign when a user registers himself. This role must already exist. Mandatory when using ldap.userRegistration\n## @param ldap.rolesMapping mapping from LDAP DN to a list of roles\n## @param ldap.rolesSyncAtLogin replace ALL the user's roles each login, or only on registration\n##\nldap:\n  enabled: false\n  uri: \"ldap://ldap_server:389\"\n  basedn: \"dc=example,dc=org\"\n  searchAttribute: \"cn\"\n  binddn: \"cn=admin,dc=example,dc=org\"\n  bindpw: \"\"\n  userRegistration: 'True'\n  userRegistrationRole: \"Public\"\n  rolesMapping: '{ \"cn=All,ou=Groups,dc=example,dc=org\": [\"User\"], \"cn=Admins,ou=Groups,dc=example,dc=org\": [\"Admin\"], }'\n  rolesSyncAtLogin: 'True'\n\n  ## SSL/TLS parameters for LDAP\n  ## @param ldap.tls.enabled Enabled TLS/SSL for LDAP, you must include the CA file.\n  ## @param ldap.tls.allowSelfSigned Allow to use self signed certificates\n  ## DEPRECATED ldap.tls.CAcertificateSecret It will be removed in a future release, please use ldap.tls.certificatesSecret instead\n  ## @param ldap.tls.certificatesSecret Name of the existing secret containing the certificate CA file that will be used by ldap client\n  ## @param ldap.tls.certificatesMountPath Where LDAP certifcates are mounted.\n  ## DEPRECATED ldap.tls.CAcertificateFilename It will be removed in a future release, please use ldap.tls.CAFilename instead\n  ## @param ldap.tls.CAFilename LDAP CA cert filename\n  ##\n  tls:\n    enabled: false\n    allowSelfSigned: true\n    certificatesSecret: \"\"\n    certificatesMountPath: /opt/bitnami/airflow/conf/certs\n    CAFilename: \"\"\n\n## @section Traffic Exposure Parameters\n\n## Airflow service parameters\n##\nservice:\n  ## @param service.type Airflow service type\n  ##\n  type: ClusterIP\n  ## @param service.ports.http Airflow service HTTP port\n  ##\n  ports:\n    http: 8080\n  ## Node ports to expose\n  ## @param service.nodePorts.http Node port for HTTP\n  ## NOTE: choose port between <30000-32767>\n  ##\n  nodePorts:\n    http: \"\"\n  ## @param service.sessionAffinity Control where client requests go, to the same pod or round-robin\n  ## Values: ClientIP or None\n  ## ref: https://kubernetes.io/docs/user-guide/services/\n  ##\n  sessionAffinity: None\n  ## @param service.sessionAffinityConfig Additional settings for the sessionAffinity\n  ## sessionAffinityConfig:\n  ##   clientIP:\n  ##     timeoutSeconds: 300\n  ##\n  sessionAffinityConfig: {}\n  ## @param service.clusterIP Airflow service Cluster IP\n  ## e.g.:\n  ## clusterIP: None\n  ##\n  clusterIP: \"\"\n  ## @param service.loadBalancerIP Airflow service Load Balancer IP\n  ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-loadbalancer\n  ##\n  loadBalancerIP: \"\"\n  ## @param service.loadBalancerSourceRanges Airflow service Load Balancer sources\n  ## ref: https://kubernetes.io/docs/tasks/access-application-cluster/configure-cloud-provider-firewall/#restrict-access-for-loadbalancer-service\n  ## e.g:\n  ## loadBalancerSourceRanges:\n  ##   - **********/24\n  ##\n  loadBalancerSourceRanges: []\n  ## @param service.externalTrafficPolicy Airflow service external traffic policy\n  ## ref https://kubernetes.io/docs/tasks/access-application-cluster/create-external-load-balancer/#preserving-the-client-source-ip\n  ##\n  externalTrafficPolicy: Cluster\n  ## @param service.annotations Additional custom annotations for Airflow service\n  ##\n  annotations: {}\n  ## @param service.extraPorts Extra port to expose on Airflow service\n  ##\n  extraPorts: []\n\n## Airflow ingress parameters\n## ref: https://kubernetes.io/docs/user-guide/ingress/\n##\ningress:\n  ## @param ingress.enabled Enable ingress record generation for Airflow\n  ##\n  enabled: false\n  ## @param ingress.ingressClassName IngressClass that will be be used to implement the Ingress (Kubernetes 1.18+)\n  ## This is supported in Kubernetes 1.18+ and required if you have more than one IngressClass marked as the default for your cluster .\n  ## ref: https://kubernetes.io/blog/2020/04/02/improvements-to-the-ingress-api-in-kubernetes-1.18/\n  ##\n  ingressClassName: \"\"\n  ## @param ingress.pathType Ingress path type\n  ##\n  pathType: ImplementationSpecific\n  ## @param ingress.apiVersion Force Ingress API version (automatically detected if not set)\n  ##\n  apiVersion: \"\"\n  ## @param ingress.hostname Default host for the ingress record\n  ##\n  hostname: airflow.local\n  ## @param ingress.path Default path for the ingress record\n  ## NOTE: You may need to set this to '/*' in order to use this with ALB ingress controllers\n  ##\n  path: /\n  ## @param ingress.annotations [object] Additional annotations for the Ingress resource. To enable certificate autogeneration, place here your cert-manager annotations.\n  ## Use this parameter to set the required annotations for cert-manager, see\n  ## ref: https://cert-manager.io/docs/usage/ingress/#supported-annotations\n  ## e.g:\n  ## annotations:\n  ##   kubernetes.io/ingress.class: nginx\n  ##   cert-manager.io/cluster-issuer: cluster-issuer-name\n  ##\n  annotations: {}\n  ## @param ingress.tls Enable TLS configuration for the host defined at `ingress.hostname` parameter\n  ## TLS certificates will be retrieved from a TLS secret with name: `{{- printf \"%s-tls\" .Values.ingress.hostname }}`\n  ## You can:\n  ##   - Use the `ingress.secrets` parameter to create this TLS secret\n  ##   - Rely on cert-manager to create it by setting the corresponding annotations\n  ##   - Rely on Helm to create self-signed certificates by setting `ingress.selfSigned=true`\n  ##\n  tls: false\n  ## @param ingress.selfSigned Create a TLS secret for this ingress record using self-signed certificates generated by Helm\n  ##\n  selfSigned: false\n  ## @param ingress.extraHosts An array with additional hostname(s) to be covered with the ingress record\n  ## e.g:\n  ## extraHosts:\n  ##   - name: airflow.local\n  ##     path: /\n  ##\n  extraHosts: []\n  ## @param ingress.extraPaths An array with additional arbitrary paths that may need to be added to the ingress under the main host\n  ## e.g:\n  ## extraPaths:\n  ## - path: /*\n  ##   backend:\n  ##     serviceName: ssl-redirect\n  ##     servicePort: use-annotation\n  ##\n  extraPaths: []\n  ## @param ingress.extraTls TLS configuration for additional hostname(s) to be covered with this ingress record\n  ## ref: https://kubernetes.io/docs/concepts/services-networking/ingress/#tls\n  ## e.g:\n  ## extraTls:\n  ## - hosts:\n  ##     - airflow.local\n  ##   secretName: airflow.local-tls\n  ##\n  extraTls: []\n  ## @param ingress.secrets Custom TLS certificates as secrets\n  ## NOTE: 'key' and 'certificate' are expected in PEM format\n  ## NOTE: 'name' should line up with a 'secretName' set further up\n  ## If it is not set and you're using cert-manager, this is unneeded, as it will create a secret for you with valid certificates\n  ## If it is not set and you're NOT using cert-manager either, self-signed certificates will be created valid for 365 days\n  ## It is also possible to create and manage the certificates outside of this helm chart\n  ## Please see README.md for more information\n  ## e.g:\n  ## secrets:\n  ##   - name: airflow.local-tls\n  ##     key: |-\n  ##       -----BEGIN RSA PRIVATE KEY-----\n  ##       ...\n  ##       -----END RSA PRIVATE KEY-----\n  ##     certificate: |-\n  ##       -----BEGIN CERTIFICATE-----\n  ##       ...\n  ##       -----END CERTIFICATE-----\n  ##\n  secrets: []\n  ## @param ingress.extraRules Additional rules to be covered with this ingress record\n  ## ref: https://kubernetes.io/docs/concepts/services-networking/ingress/#ingress-rules\n  ## e.g:\n  ## extraRules:\n  ## - host: example.local\n  ##     http:\n  ##       path: /\n  ##       backend:\n  ##         service:\n  ##           name: example-svc\n  ##           port:\n  ##             name: http\n  ##\n  extraRules: []\n\n## @section Other Parameters\n\n## Service account for Airflow pods to use.\n## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-service-account/\n##\nserviceAccount:\n  ## @param serviceAccount.create Enable creation of ServiceAccount for Airflow pods\n  ##\n  create: false\n  ## @param serviceAccount.name The name of the ServiceAccount to use.\n  ## If not set and create is true, a name is generated using the common.names.fullname template\n  ##\n  name: \"\"\n  ## @param serviceAccount.automountServiceAccountToken Allows auto mount of ServiceAccountToken on the serviceAccount created\n  ## Can be set to false if pods using this serviceAccount do not need to use K8s API\n  ##\n  automountServiceAccountToken: true\n  ## @param serviceAccount.annotations Additional custom annotations for the ServiceAccount\n  ##\n  annotations: {}\n## Role Based Access\n## Ref: https://kubernetes.io/docs/admin/authorization/rbac/\n## @param rbac.create Create Role and RoleBinding\n##\nrbac:\n  create: false\n  ## @param rbac.rules Custom RBAC rules to set\n  ## e.g:\n  ## rules:\n  ##   - apiGroups:\n  ##       - \"\"\n  ##     resources:\n  ##       - pods\n  ##     verbs:\n  ##       - get\n  ##       - list\n  ##\n  rules: []\n\n## @section Airflow metrics parameters\n\nmetrics:\n  ## @param metrics.enabled Whether or not to create a standalone Airflow exporter to expose Airflow metrics\n  ##\n  enabled: false\n  ## Bitnami Airflow exporter image\n  ## ref: https://hub.docker.com/r/bitnami/airflow-exporter/tags/\n  ## @param metrics.image.registry Airflow exporter image registry\n  ## @param metrics.image.repository Airflow exporter image repository\n  ## @param metrics.image.tag Airflow exporter image tag (immutable tags are recommended)\n  ## @param metrics.image.digest Airflow exporter image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag\n  ## @param metrics.image.pullPolicy Airflow exporter image pull policy\n  ## @param metrics.image.pullSecrets Airflow exporter image pull secrets\n  ##\n  image:\n    registry: docker.io\n    repository: bitnami/airflow-exporter\n    tag: 0.20220314.0-debian-11-r57\n    digest: \"\"\n    pullPolicy: IfNotPresent\n    ## Optionally specify an array of imagePullSecrets.\n    ## Secrets must be manually created in the namespace.\n    ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/\n    ## e.g:\n    ## pullSecrets:\n    ##   - myRegistryKeySecretName\n    ##\n    pullSecrets: []\n  ## @param metrics.extraEnvVars Array with extra environment variables to add Airflow exporter pods\n  ##\n  extraEnvVars: []\n  ## @param metrics.extraEnvVarsCM ConfigMap containing extra environment variables for Airflow exporter pods\n  ##\n  extraEnvVarsCM: \"\"\n  ## @param metrics.extraEnvVarsSecret Secret containing extra environment variables (in case of sensitive data) for Airflow exporter pods\n  ##\n  extraEnvVarsSecret: \"\"\n  ## @param metrics.containerPorts.http Airflow exporter metrics container port\n  ##\n  containerPorts:\n    http: 9112\n  ## Airflow exporter resource requests and limits\n  ## ref: https://kubernetes.io/docs/user-guide/compute-resources/\n  ## @param metrics.resources.limits The resources limits for the container\n  ## @param metrics.resources.requests The requested resources for the container\n  ##\n  resources:\n    limits: {}\n    requests: {}\n  ## Airflow exporter pods' Security Context\n  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod\n  ## @param metrics.podSecurityContext.enabled Enable security context for the pods\n  ## @param metrics.podSecurityContext.fsGroup Set Airflow exporter pod's Security Context fsGroup\n  ##\n  podSecurityContext:\n    enabled: true\n    fsGroup: 1001\n  ## Airflow exporter containers' Security Context\n  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container\n  ## @param metrics.containerSecurityContext.enabled Enable Airflow exporter containers' Security Context\n  ## @param metrics.containerSecurityContext.runAsUser Set Airflow exporter containers' Security Context runAsUser\n  ## @param metrics.containerSecurityContext.runAsNonRoot Set Airflow exporter containers' Security Context runAsNonRoot\n  ## e.g:\n  ##   containerSecurityContext:\n  ##     enabled: true\n  ##     capabilities:\n  ##       drop: [\"NET_RAW\"]\n  ##     readOnlyRootFilesystem: true\n  ##\n  containerSecurityContext:\n    enabled: true\n    runAsUser: 1001\n    runAsNonRoot: true\n  ## @param metrics.lifecycleHooks for the Airflow exporter container(s) to automate configuration before or after startup\n  ##\n  lifecycleHooks: {}\n  ## @param metrics.hostAliases Airflow exporter pods host aliases\n  ## https://kubernetes.io/docs/concepts/services-networking/add-entries-to-pod-etc-hosts-with-host-aliases/\n  ##\n  hostAliases: []\n  ## @param metrics.podLabels Extra labels for Airflow exporter pods\n  ## Ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/\n  ##\n  podLabels: {}\n  ## @param metrics.podAnnotations Extra annotations for Airflow exporter pods\n  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/\n  ##\n  podAnnotations: {}\n  ## @param metrics.podAffinityPreset Pod affinity preset. Ignored if `metrics.affinity` is set. Allowed values: `soft` or `hard`\n  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity\n  ##\n  podAffinityPreset: \"\"\n  ## @param metrics.podAntiAffinityPreset Pod anti-affinity preset. Ignored if `metrics.affinity` is set. Allowed values: `soft` or `hard`\n  ## Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity\n  ##\n  podAntiAffinityPreset: soft\n  ## Node metrics.affinity preset\n  ## Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#node-affinity\n  ##\n  nodeAffinityPreset:\n    ## @param metrics.nodeAffinityPreset.type Node affinity preset type. Ignored if `metrics.affinity` is set. Allowed values: `soft` or `hard`\n    ##\n    type: \"\"\n    ## @param metrics.nodeAffinityPreset.key Node label key to match Ignored if `metrics.affinity` is set.\n    ## E.g.\n    ## key: \"kubernetes.io/e2e-az-name\"\n    ##\n    key: \"\"\n    ## @param metrics.nodeAffinityPreset.values Node label values to match. Ignored if `metrics.affinity` is set.\n    ## E.g.\n    ## values:\n    ##   - e2e-az1\n    ##   - e2e-az2\n    ##\n    values: []\n  ## @param metrics.affinity Affinity for pod assignment\n  ## Ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity\n  ## Note: metrics.podAffinityPreset, metrics.podAntiAffinityPreset, and metrics.nodeAffinityPreset will be ignored when it's set\n  ##\n  affinity: {}\n  ## @param metrics.nodeSelector Node labels for pod assignment\n  ## Ref: https://kubernetes.io/docs/user-guide/node-selection/\n  ##\n  nodeSelector: {}\n  ## @param metrics.tolerations Tolerations for pod assignment\n  ## Ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/\n  ##\n  tolerations: []\n  ## @param metrics.schedulerName Name of the k8s scheduler (other than default) for Airflow exporter\n  ## ref: https://kubernetes.io/docs/tasks/administer-cluster/configure-multiple-schedulers/\n  ##\n  schedulerName: \"\"\n  ## Airflow exporter service configuration\n  ##\n  service:\n    ## @param metrics.service.ports.http Airflow exporter metrics service port\n    ##\n    ports:\n      http: 9112\n    ## @param metrics.service.clusterIP Static clusterIP or None for headless services\n    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#choosing-your-own-ip-address\n    ##\n    clusterIP: \"\"\n    ## @param metrics.service.sessionAffinity Control where client requests go, to the same pod or round-robin\n    ## Values: ClientIP or None\n    ## ref: https://kubernetes.io/docs/user-guide/services/\n    ##\n    sessionAffinity: None\n    ## @param metrics.service.annotations [object] Annotations for the Airflow exporter service\n    ##\n    annotations:\n      prometheus.io/scrape: \"true\"\n      prometheus.io/port: \"{{ .Values.metrics.service.ports.http }}\"\n  ## Prometheus Operator ServiceMonitor configuration\n  ##\n  serviceMonitor:\n    ## @param metrics.serviceMonitor.enabled if `true`, creates a Prometheus Operator ServiceMonitor (requires `metrics.enabled` to be `true`)\n    ##\n    enabled: false\n    ## @param metrics.serviceMonitor.namespace Namespace in which Prometheus is running\n    ##\n    namespace: \"\"\n    ## @param metrics.serviceMonitor.interval Interval at which metrics should be scraped\n    ## ref: https://github.com/coreos/prometheus-operator/blob/master/Documentation/api.md#endpoint\n    ##\n    interval: \"\"\n    ## @param metrics.serviceMonitor.scrapeTimeout Timeout after which the scrape is ended\n    ## ref: https://github.com/coreos/prometheus-operator/blob/master/Documentation/api.md#endpoint\n    ##\n    scrapeTimeout: \"\"\n    ## @param metrics.serviceMonitor.labels Additional labels that can be used so ServiceMonitor will be discovered by Prometheus\n    ##\n    labels: {}\n    ## @param metrics.serviceMonitor.selector Prometheus instance selector labels\n    ## ref: https://github.com/bitnami/charts/tree/main/bitnami/prometheus-operator#prometheus-configuration\n    ##\n    selector: {}\n    ## @param metrics.serviceMonitor.relabelings RelabelConfigs to apply to samples before scraping\n    ##\n    relabelings: []\n    ## @param metrics.serviceMonitor.metricRelabelings MetricRelabelConfigs to apply to samples before ingestion\n    ##\n    metricRelabelings: []\n    ## @param metrics.serviceMonitor.honorLabels Specify honorLabels parameter to add the scrape endpoint\n    ##\n    honorLabels: false\n    ## @param metrics.serviceMonitor.jobLabel The name of the label on the target service to use as the job name in prometheus.\n    ##\n    jobLabel: \"\"\n\n## @section Airflow database parameters\n\n## PostgreSQL chart configuration\n## ref: https://github.com/bitnami/charts/blob/main/bitnami/postgresql/values.yaml\n## @param postgresql.enabled Switch to enable or disable the PostgreSQL helm chart\n## @param postgresql.auth.enablePostgresUser Assign a password to the \"postgres\" admin user. Otherwise, remote access will be blocked for this user\n## @param postgresql.auth.username Name for a custom user to create\n## @param postgresql.auth.password Password for the custom user to create\n## @param postgresql.auth.database Name for a custom database to create\n## @param postgresql.auth.existingSecret Name of existing secret to use for PostgreSQL credentials\n## @param postgresql.architecture PostgreSQL architecture (`standalone` or `replication`)\n##\npostgresql:\n  enabled: true\n  auth:\n    enablePostgresUser: false\n    username: bn_airflow\n    password: \"\"\n    database: bitnami_airflow\n    existingSecret: \"\"\n  architecture: standalone\n## External PostgreSQL configuration\n## All of these values are only used when postgresql.enabled is set to false\n## @param externalDatabase.host Database host\n## @param externalDatabase.port Database port number\n## @param externalDatabase.user Non-root username for Airflow\n## @param externalDatabase.password Password for the non-root username for Airflow\n## @param externalDatabase.database Airflow database name\n## @param externalDatabase.existingSecret Name of an existing secret resource containing the database credentials\n## @param externalDatabase.existingSecretPasswordKey Name of an existing secret key containing the database credentials\n##\nexternalDatabase:\n  host: localhost\n  port: 5432\n  user: bn_airflow\n  database: bitnami_airflow\n  password: \"\"\n  existingSecret: \"\"\n  existingSecretPasswordKey: \"\"\n\n## Redis&reg; chart configuration\n## ref: https://github.com/bitnami/charts/blob/main/bitnami/redis/values.yaml\n## @param redis.enabled Switch to enable or disable the Redis&reg; helm\n## @param redis.auth.enabled Enable password authentication\n## @param redis.auth.password Redis&reg; password\n## @param redis.auth.existingSecret The name of an existing secret with Redis&reg; credentials\n## @param redis.architecture Redis&reg; architecture. Allowed values: `standalone` or `replication`\n##\nredis:\n  enabled: true\n  auth:\n    enabled: true\n    ## Redis&reg; password (both master and slave). Defaults to a random 10-character alphanumeric string if not set and auth.enabled is true.\n    ## It should always be set using the password value or in the existingSecret to avoid issues\n    ## with Airflow.\n    ## The password value is ignored if existingSecret is set\n    password: \"\"\n    existingSecret: \"\"\n  architecture: standalone\n\n## External Redis&reg; configuration\n## All of these values are only used when redis.enabled is set to false\n## @param externalRedis.host Redis&reg; host\n## @param externalRedis.port Redis&reg; port number\n## @param externalRedis.username Redis&reg; username\n## @param externalRedis.password Redis&reg; password\n## @param externalRedis.existingSecret Name of an existing secret resource containing the Redis&trade credentials\n## @param externalRedis.existingSecretPasswordKey Name of an existing secret key containing the Redis&trade credentials\n##\nexternalRedis:\n  host: localhost\n  port: 6379\n  ## Most Redis&reg; implementations do not require a username\n  ## to authenticate and it should be enough with the password\n  username: \"\"\n  password: \"\"\n  existingSecret: \"\"\n  existingSecretPasswordKey: \"\"\n",
			Readme:                    "",
			UserId:                    6,
			ReferenceValueId:          6304,
			ReferenceValueKind:        "DEFAULT",
			ACDAppName:                "",
			Environment:               nil,
			ChartGroupEntryId:         0,
			Status:                    appStoreBean.WF_UNKNOWN,
			AppStoreId:                0,
			AppStoreName:              "",
			Deprecated:                false,
			ForceDelete:               false,
			ClusterId:                 0,
			Namespace:                 "devtron-demo",
			AppOfferingMode:           "",
			GitOpsRepoURL:             "",
			GitOpsPath:                "",
			GitHash:                   "",
			EnvironmentName:           "",
			InstallAppVersionChartDTO: nil,
			DeploymentAppType:         "helm",
		}

		installedAppVersion, err := AppStoreDeploymentService.AppStoreDeployOperationDB(&InstallAppVersionDTO, tx, appStoreBean.INSTALL_APP_REQUEST)

		assert.Nil(t, err)
		assert.Equal(t, installedAppVersion.DeploymentAppType, "argo_cd")

	})

}

func initAppStoreDeploymentService(t *testing.T, internalUse bool) *AppStoreDeploymentDBServiceImpl {
	sugaredLogger, _ := util.InitLogger()

	config, _ := sql.GetConfig()
	db, _ := sql.NewDbConnection(config, sugaredLogger)
	attributeRepo := repository.NewAttributesRepositoryImpl(db)
	discoverRepository := appStoreDiscoverRepository.NewAppStoreApplicationVersionRepositoryImpl(sugaredLogger, db)
	environmentRepository := repository4.NewEnvironmentRepositoryImpl(db, sugaredLogger, nil)

	k8sUtil := util2.NewK8sUtil(sugaredLogger, &util2.RuntimeConfig{LocalDevMode: true})

	clusterRepository := repository2.NewClusterRepositoryImpl(db, sugaredLogger)
	defaultAuthPolicyRepositoryImpl := repository5.NewDefaultAuthPolicyRepositoryImpl(db, sugaredLogger)
	defaultAuthRoleRepositoryImpl := repository5.NewDefaultAuthRoleRepositoryImpl(db, sugaredLogger)
	userAuthRepositoryImpl := repository5.NewUserAuthRepositoryImpl(db, sugaredLogger, defaultAuthPolicyRepositoryImpl, defaultAuthRoleRepositoryImpl)
	userRepositoryImpl := repository5.NewUserRepositoryImpl(db, sugaredLogger)
	roleGroupRepositoryImpl := repository5.NewRoleGroupRepositoryImpl(db, sugaredLogger)
	clusterService := cluster.NewClusterServiceImpl(clusterRepository,
		sugaredLogger,
		k8sUtil,
		nil,
		userAuthRepositoryImpl,
		userRepositoryImpl,
		roleGroupRepositoryImpl)

	environmentService := environment.NewEnvironmentServiceImpl(environmentRepository, clusterService, sugaredLogger, k8sUtil, nil, nil, nil)
	envVariables := &util3.EnvironmentVariables{
		DeploymentServiceTypeConfig: &util3.DeploymentServiceTypeConfig{
			ExternallyManagedDeploymentType: internalUse,
		},
	}
	AppRepository := app.NewAppRepositoryImpl(db, sugaredLogger)
	InstalledAppRepository := repository3.NewInstalledAppRepositoryImpl(sugaredLogger, db)
	InstalledAppVersionHistoryRepository := repository3.NewInstalledAppVersionHistoryRepositoryImpl(sugaredLogger, db)
	attributeService := attributes.NewAttributesServiceImpl(sugaredLogger, attributeRepo)
	deploymentOverrideService := providerConfig.NewDeploymentTypeOverrideServiceImpl(sugaredLogger, envVariables, attributeService)
	appStoreDeploymentDBServiceImpl := NewAppStoreDeploymentDBServiceImpl(
		sugaredLogger,
		InstalledAppRepository,
		discoverRepository,
		AppRepository,
		environmentService,
		clusterService,
		InstalledAppVersionHistoryRepository,
		envVariables,
		nil,
		deploymentOverrideService,
		nil)

	return appStoreDeploymentDBServiceImpl
}
