// Code generated by mockery v2.18.0. DO NOT EDIT.

package mocks

import (
	"github.com/devtron-labs/devtron/pkg/auth/user/bean"
	"github.com/devtron-labs/devtron/pkg/auth/user/repository"
	mock "github.com/stretchr/testify/mock"

	pg "github.com/go-pg/pg"
)

// UserRepository is an autogenerated mock type for the UserRepository type
type UserRepository struct {
	mock.Mock
}

// CreateUser provides a mock function with given fields: userModel, tx
func (_m *UserRepository) CreateUser(userModel *repository.UserModel, tx *pg.Tx) (*repository.UserModel, error) {
	ret := _m.Called(userModel, tx)

	var r0 *repository.UserModel
	if rf, ok := ret.Get(0).(func(*repository.UserModel, *pg.Tx) *repository.UserModel); ok {
		r0 = rf(userModel, tx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.UserModel)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(*repository.UserModel, *pg.Tx) error); ok {
		r1 = rf(userModel, tx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchActiveOrDeletedUserByEmail provides a mock function with given fields: email
func (_m *UserRepository) FetchActiveOrDeletedUserByEmail(email string) (*repository.UserModel, error) {
	ret := _m.Called(email)

	var r0 *repository.UserModel
	if rf, ok := ret.Get(0).(func(string) *repository.UserModel); ok {
		r0 = rf(email)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.UserModel)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(email)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchActiveUserByEmail provides a mock function with given fields: email
func (_m *UserRepository) FetchActiveUserByEmail(email string) (bean.UserInfo, error) {
	ret := _m.Called(email)

	var r0 bean.UserInfo
	if rf, ok := ret.Get(0).(func(string) bean.UserInfo); ok {
		r0 = rf(email)
	} else {
		r0 = ret.Get(0).(bean.UserInfo)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(email)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchUserDetailByEmail provides a mock function with given fields: email
func (_m *UserRepository) FetchUserDetailByEmail(email string) (bean.UserInfo, error) {
	ret := _m.Called(email)

	var r0 bean.UserInfo
	if rf, ok := ret.Get(0).(func(string) bean.UserInfo); ok {
		r0 = rf(email)
	} else {
		r0 = ret.Get(0).(bean.UserInfo)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(email)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchUserMatchesByEmailIdExcludingApiTokenUser provides a mock function with given fields: email
func (_m *UserRepository) FetchUserMatchesByEmailIdExcludingApiTokenUser(email string) ([]repository.UserModel, error) {
	ret := _m.Called(email)

	var r0 []repository.UserModel
	if rf, ok := ret.Get(0).(func(string) []repository.UserModel); ok {
		r0 = rf(email)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]repository.UserModel)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(email)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllExcludingApiTokenUser provides a mock function with given fields:
func (_m *UserRepository) GetAllExcludingApiTokenUser() ([]repository.UserModel, error) {
	ret := _m.Called()

	var r0 []repository.UserModel
	if rf, ok := ret.Get(0).(func() []repository.UserModel); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]repository.UserModel)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetById provides a mock function with given fields: id
func (_m *UserRepository) GetById(id int32) (*repository.UserModel, error) {
	ret := _m.Called(id)

	var r0 *repository.UserModel
	if rf, ok := ret.Get(0).(func(int32) *repository.UserModel); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.UserModel)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int32) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByIdIncludeDeleted provides a mock function with given fields: id
func (_m *UserRepository) GetByIdIncludeDeleted(id int32) (*repository.UserModel, error) {
	ret := _m.Called(id)

	var r0 *repository.UserModel
	if rf, ok := ret.Get(0).(func(int32) *repository.UserModel); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.UserModel)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int32) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByIds provides a mock function with given fields: ids
func (_m *UserRepository) GetByIds(ids []int32) ([]repository.UserModel, error) {
	ret := _m.Called(ids)

	var r0 []repository.UserModel
	if rf, ok := ret.Get(0).(func([]int32) []repository.UserModel); ok {
		r0 = rf(ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]repository.UserModel)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func([]int32) error); ok {
		r1 = rf(ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetConnection provides a mock function with given fields:
func (_m *UserRepository) GetConnection() *pg.DB {
	ret := _m.Called()

	var r0 *pg.DB
	if rf, ok := ret.Get(0).(func() *pg.DB); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pg.DB)
		}
	}

	return r0
}

// UpdateRoleIdForUserRolesMappings provides a mock function with given fields: roleId, newRoleId
func (_m *UserRepository) UpdateRoleIdForUserRolesMappings(roleId int, newRoleId int) (*repository.UserRoleModel, error) {
	ret := _m.Called(roleId, newRoleId)

	var r0 *repository.UserRoleModel
	if rf, ok := ret.Get(0).(func(int, int) *repository.UserRoleModel); ok {
		r0 = rf(roleId, newRoleId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.UserRoleModel)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, int) error); ok {
		r1 = rf(roleId, newRoleId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateUser provides a mock function with given fields: userModel, tx
func (_m *UserRepository) UpdateUser(userModel *repository.UserModel, tx *pg.Tx) (*repository.UserModel, error) {
	ret := _m.Called(userModel, tx)

	var r0 *repository.UserModel
	if rf, ok := ret.Get(0).(func(*repository.UserModel, *pg.Tx) *repository.UserModel); ok {
		r0 = rf(userModel, tx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.UserModel)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(*repository.UserModel, *pg.Tx) error); ok {
		r1 = rf(userModel, tx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewUserRepository interface {
	mock.TestingT
	Cleanup(func())
}

// NewUserRepository creates a new instance of UserRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewUserRepository(t mockConstructorTestingTNewUserRepository) *UserRepository {
	mock := &UserRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
