// Code generated by mockery v2.42.0. DO NOT EDIT.

package mocks

import (
	context "context"

	pg "github.com/go-pg/pg"
	mock "github.com/stretchr/testify/mock"

	pipelineConfig "github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
)

// PipelineRepository is an autogenerated mock type for the PipelineRepository type
type PipelineRepository struct {
	mock.Mock
}

// Delete provides a mock function with given fields: id, userId, tx
func (_m *PipelineRepository) Delete(id int, userId int32, tx *pg.Tx) error {
	ret := _m.Called(id, userId, tx)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int, int32, *pg.Tx) error); ok {
		r0 = rf(id, userId, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// FilterDeploymentDeleteRequestedPipelineIds provides a mock function with given fields: cdPipelineIds
func (_m *PipelineRepository) FilterDeploymentDeleteRequestedPipelineIds(cdPipelineIds []int) (map[int]bool, error) {
	ret := _m.Called(cdPipelineIds)

	if len(ret) == 0 {
		panic("no return value specified for FilterDeploymentDeleteRequestedPipelineIds")
	}

	var r0 map[int]bool
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) (map[int]bool, error)); ok {
		return rf(cdPipelineIds)
	}
	if rf, ok := ret.Get(0).(func([]int) map[int]bool); ok {
		r0 = rf(cdPipelineIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[int]bool)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(cdPipelineIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindActiveByAppId provides a mock function with given fields: appId
func (_m *PipelineRepository) FindActiveByAppId(appId int) ([]*pipelineConfig.Pipeline, error) {
	ret := _m.Called(appId)

	if len(ret) == 0 {
		panic("no return value specified for FindActiveByAppId")
	}

	var r0 []*pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]*pipelineConfig.Pipeline, error)); ok {
		return rf(appId)
	}
	if rf, ok := ret.Get(0).(func(int) []*pipelineConfig.Pipeline); ok {
		r0 = rf(appId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(appId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindActiveByAppIdAndEnvId provides a mock function with given fields: appId, envId
func (_m *PipelineRepository) FindActiveByAppIdAndEnvId(appId int, envId int) (*pipelineConfig.Pipeline, error) {
	ret := _m.Called(appId, envId)

	if len(ret) == 0 {
		panic("no return value specified for FindActiveByAppIdAndEnvId")
	}

	var r0 *pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int, int) (*pipelineConfig.Pipeline, error)); ok {
		return rf(appId, envId)
	}
	if rf, ok := ret.Get(0).(func(int, int) *pipelineConfig.Pipeline); ok {
		r0 = rf(appId, envId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int, int) error); ok {
		r1 = rf(appId, envId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindActiveByAppIdAndEnvironmentId provides a mock function with given fields: appId, environmentId
func (_m *PipelineRepository) FindActiveByAppIdAndEnvironmentId(appId int, environmentId int) ([]*pipelineConfig.Pipeline, error) {
	ret := _m.Called(appId, environmentId)

	if len(ret) == 0 {
		panic("no return value specified for FindActiveByAppIdAndEnvironmentId")
	}

	var r0 []*pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int, int) ([]*pipelineConfig.Pipeline, error)); ok {
		return rf(appId, environmentId)
	}
	if rf, ok := ret.Get(0).(func(int, int) []*pipelineConfig.Pipeline); ok {
		r0 = rf(appId, environmentId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int, int) error); ok {
		r1 = rf(appId, environmentId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindActiveByAppIdAndEnvironmentIdV2 provides a mock function with given fields:
func (_m *PipelineRepository) FindActiveByAppIdAndEnvironmentIdV2() ([]*pipelineConfig.Pipeline, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for FindActiveByAppIdAndEnvironmentIdV2")
	}

	var r0 []*pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]*pipelineConfig.Pipeline, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []*pipelineConfig.Pipeline); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindActiveByAppIdAndPipelineId provides a mock function with given fields: appId, pipelineId
func (_m *PipelineRepository) FindActiveByAppIdAndPipelineId(appId int, pipelineId int) ([]*pipelineConfig.Pipeline, error) {
	ret := _m.Called(appId, pipelineId)

	if len(ret) == 0 {
		panic("no return value specified for FindActiveByAppIdAndPipelineId")
	}

	var r0 []*pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int, int) ([]*pipelineConfig.Pipeline, error)); ok {
		return rf(appId, pipelineId)
	}
	if rf, ok := ret.Get(0).(func(int, int) []*pipelineConfig.Pipeline); ok {
		r0 = rf(appId, pipelineId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int, int) error); ok {
		r1 = rf(appId, pipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindActiveByAppIds provides a mock function with given fields: appIds
func (_m *PipelineRepository) FindActiveByAppIds(appIds []int) ([]*pipelineConfig.Pipeline, error) {
	ret := _m.Called(appIds)

	if len(ret) == 0 {
		panic("no return value specified for FindActiveByAppIds")
	}

	var r0 []*pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) ([]*pipelineConfig.Pipeline, error)); ok {
		return rf(appIds)
	}
	if rf, ok := ret.Get(0).(func([]int) []*pipelineConfig.Pipeline); ok {
		r0 = rf(appIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(appIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindActiveByEnvId provides a mock function with given fields: envId
func (_m *PipelineRepository) FindActiveByEnvId(envId int) ([]*pipelineConfig.Pipeline, error) {
	ret := _m.Called(envId)

	if len(ret) == 0 {
		panic("no return value specified for FindActiveByEnvId")
	}

	var r0 []*pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]*pipelineConfig.Pipeline, error)); ok {
		return rf(envId)
	}
	if rf, ok := ret.Get(0).(func(int) []*pipelineConfig.Pipeline); ok {
		r0 = rf(envId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(envId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindActiveByEnvIdAndDeploymentType provides a mock function with given fields: environmentId, deploymentAppType, exclusionList, includeApps
func (_m *PipelineRepository) FindActiveByEnvIdAndDeploymentType(environmentId int, deploymentAppType string, exclusionList []int, includeApps []int) ([]*pipelineConfig.Pipeline, error) {
	ret := _m.Called(environmentId, deploymentAppType, exclusionList, includeApps)

	if len(ret) == 0 {
		panic("no return value specified for FindActiveByEnvIdAndDeploymentType")
	}

	var r0 []*pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int, string, []int, []int) ([]*pipelineConfig.Pipeline, error)); ok {
		return rf(environmentId, deploymentAppType, exclusionList, includeApps)
	}
	if rf, ok := ret.Get(0).(func(int, string, []int, []int) []*pipelineConfig.Pipeline); ok {
		r0 = rf(environmentId, deploymentAppType, exclusionList, includeApps)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int, string, []int, []int) error); ok {
		r1 = rf(environmentId, deploymentAppType, exclusionList, includeApps)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindActiveByEnvIds provides a mock function with given fields: envId
func (_m *PipelineRepository) FindActiveByEnvIds(envId []int) ([]*pipelineConfig.Pipeline, error) {
	ret := _m.Called(envId)

	if len(ret) == 0 {
		panic("no return value specified for FindActiveByEnvIds")
	}

	var r0 []*pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) ([]*pipelineConfig.Pipeline, error)); ok {
		return rf(envId)
	}
	if rf, ok := ret.Get(0).(func([]int) []*pipelineConfig.Pipeline); ok {
		r0 = rf(envId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(envId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindActiveByInFilter provides a mock function with given fields: envId, appIdIncludes
func (_m *PipelineRepository) FindActiveByInFilter(envId int, appIdIncludes []int) ([]*pipelineConfig.Pipeline, error) {
	ret := _m.Called(envId, appIdIncludes)

	if len(ret) == 0 {
		panic("no return value specified for FindActiveByInFilter")
	}

	var r0 []*pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int, []int) ([]*pipelineConfig.Pipeline, error)); ok {
		return rf(envId, appIdIncludes)
	}
	if rf, ok := ret.Get(0).(func(int, []int) []*pipelineConfig.Pipeline); ok {
		r0 = rf(envId, appIdIncludes)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int, []int) error); ok {
		r1 = rf(envId, appIdIncludes)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindActiveByNotFilter provides a mock function with given fields: envId, appIdExcludes
func (_m *PipelineRepository) FindActiveByNotFilter(envId int, appIdExcludes []int) ([]*pipelineConfig.Pipeline, error) {
	ret := _m.Called(envId, appIdExcludes)

	if len(ret) == 0 {
		panic("no return value specified for FindActiveByNotFilter")
	}

	var r0 []*pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int, []int) ([]*pipelineConfig.Pipeline, error)); ok {
		return rf(envId, appIdExcludes)
	}
	if rf, ok := ret.Get(0).(func(int, []int) []*pipelineConfig.Pipeline); ok {
		r0 = rf(envId, appIdExcludes)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int, []int) error); ok {
		r1 = rf(envId, appIdExcludes)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindActivePipelineAppIdsByEnvId provides a mock function with given fields: envId
func (_m *PipelineRepository) FindActivePipelineAppIdsByEnvId(envId int) ([]int, error) {
	ret := _m.Called(envId)

	if len(ret) == 0 {
		panic("no return value specified for FindActivePipelineAppIdsByEnvId")
	}

	var r0 []int
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]int, error)); ok {
		return rf(envId)
	}
	if rf, ok := ret.Get(0).(func(int) []int); ok {
		r0 = rf(envId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]int)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(envId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindActivePipelineAppIdsByInFilter provides a mock function with given fields: envId, appIdIncludes
func (_m *PipelineRepository) FindActivePipelineAppIdsByInFilter(envId int, appIdIncludes []int) ([]int, error) {
	ret := _m.Called(envId, appIdIncludes)

	if len(ret) == 0 {
		panic("no return value specified for FindActivePipelineAppIdsByInFilter")
	}

	var r0 []int
	var r1 error
	if rf, ok := ret.Get(0).(func(int, []int) ([]int, error)); ok {
		return rf(envId, appIdIncludes)
	}
	if rf, ok := ret.Get(0).(func(int, []int) []int); ok {
		r0 = rf(envId, appIdIncludes)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]int)
		}
	}

	if rf, ok := ret.Get(1).(func(int, []int) error); ok {
		r1 = rf(envId, appIdIncludes)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindActivePipelineByEnvId provides a mock function with given fields: envId
func (_m *PipelineRepository) FindActivePipelineByEnvId(envId int) ([]*pipelineConfig.Pipeline, error) {
	ret := _m.Called(envId)

	if len(ret) == 0 {
		panic("no return value specified for FindActivePipelineByEnvId")
	}

	var r0 []*pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]*pipelineConfig.Pipeline, error)); ok {
		return rf(envId)
	}
	if rf, ok := ret.Get(0).(func(int) []*pipelineConfig.Pipeline); ok {
		r0 = rf(envId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(envId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAllDeletedPipelineCountInLast24Hour provides a mock function with given fields:
func (_m *PipelineRepository) FindAllDeletedPipelineCountInLast24Hour() (int, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for FindAllDeletedPipelineCountInLast24Hour")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func() (int, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() int); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAllPipelineCreatedCountInLast24Hour provides a mock function with given fields:
func (_m *PipelineRepository) FindAllPipelineCreatedCountInLast24Hour() (int, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for FindAllPipelineCreatedCountInLast24Hour")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func() (int, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() int); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAllPipelinesByChartsOverrideAndAppIdAndChartId provides a mock function with given fields: chartOverridden, appId, chartId
func (_m *PipelineRepository) FindAllPipelinesByChartsOverrideAndAppIdAndChartId(chartOverridden bool, appId int, chartId int) ([]*pipelineConfig.Pipeline, error) {
	ret := _m.Called(chartOverridden, appId, chartId)

	if len(ret) == 0 {
		panic("no return value specified for FindAllPipelinesByChartsOverrideAndAppIdAndChartId")
	}

	var r0 []*pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(bool, int, int) ([]*pipelineConfig.Pipeline, error)); ok {
		return rf(chartOverridden, appId, chartId)
	}
	if rf, ok := ret.Get(0).(func(bool, int, int) []*pipelineConfig.Pipeline); ok {
		r0 = rf(chartOverridden, appId, chartId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(bool, int, int) error); ok {
		r1 = rf(chartOverridden, appId, chartId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAppAndEnvDetailsByPipelineId provides a mock function with given fields: id
func (_m *PipelineRepository) FindAppAndEnvDetailsByPipelineId(id int) (*pipelineConfig.Pipeline, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for FindAppAndEnvDetailsByPipelineId")
	}

	var r0 *pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*pipelineConfig.Pipeline, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) *pipelineConfig.Pipeline); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAppAndEnvironmentAndProjectByPipelineIds provides a mock function with given fields: pipelineIds
func (_m *PipelineRepository) FindAppAndEnvironmentAndProjectByPipelineIds(pipelineIds []int) ([]*pipelineConfig.Pipeline, error) {
	ret := _m.Called(pipelineIds)

	if len(ret) == 0 {
		panic("no return value specified for FindAppAndEnvironmentAndProjectByPipelineIds")
	}

	var r0 []*pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) ([]*pipelineConfig.Pipeline, error)); ok {
		return rf(pipelineIds)
	}
	if rf, ok := ret.Get(0).(func([]int) []*pipelineConfig.Pipeline); ok {
		r0 = rf(pipelineIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(pipelineIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAutomaticByCiPipelineId provides a mock function with given fields: ciPipelineId
func (_m *PipelineRepository) FindAutomaticByCiPipelineId(ciPipelineId int) ([]*pipelineConfig.Pipeline, error) {
	ret := _m.Called(ciPipelineId)

	if len(ret) == 0 {
		panic("no return value specified for FindAutomaticByCiPipelineId")
	}

	var r0 []*pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]*pipelineConfig.Pipeline, error)); ok {
		return rf(ciPipelineId)
	}
	if rf, ok := ret.Get(0).(func(int) []*pipelineConfig.Pipeline); ok {
		r0 = rf(ciPipelineId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(ciPipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByAppIdToEnvIdsMapping provides a mock function with given fields: appIdToEnvIds
func (_m *PipelineRepository) FindByAppIdToEnvIdsMapping(appIdToEnvIds map[int][]int) ([]*pipelineConfig.Pipeline, error) {
	ret := _m.Called(appIdToEnvIds)

	if len(ret) == 0 {
		panic("no return value specified for FindByAppIdToEnvIdsMapping")
	}

	var r0 []*pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(map[int][]int) ([]*pipelineConfig.Pipeline, error)); ok {
		return rf(appIdToEnvIds)
	}
	if rf, ok := ret.Get(0).(func(map[int][]int) []*pipelineConfig.Pipeline); ok {
		r0 = rf(appIdToEnvIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(map[int][]int) error); ok {
		r1 = rf(appIdToEnvIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByCiPipelineId provides a mock function with given fields: ciPipelineId
func (_m *PipelineRepository) FindByCiPipelineId(ciPipelineId int) ([]*pipelineConfig.Pipeline, error) {
	ret := _m.Called(ciPipelineId)

	if len(ret) == 0 {
		panic("no return value specified for FindByCiPipelineId")
	}

	var r0 []*pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]*pipelineConfig.Pipeline, error)); ok {
		return rf(ciPipelineId)
	}
	if rf, ok := ret.Get(0).(func(int) []*pipelineConfig.Pipeline); ok {
		r0 = rf(ciPipelineId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(ciPipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByCiPipelineIdsIn provides a mock function with given fields: ciPipelineIds
func (_m *PipelineRepository) FindByCiPipelineIdsIn(ciPipelineIds []int) ([]*pipelineConfig.Pipeline, error) {
	ret := _m.Called(ciPipelineIds)

	if len(ret) == 0 {
		panic("no return value specified for FindByCiPipelineIdsIn")
	}

	var r0 []*pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) ([]*pipelineConfig.Pipeline, error)); ok {
		return rf(ciPipelineIds)
	}
	if rf, ok := ret.Get(0).(func([]int) []*pipelineConfig.Pipeline); ok {
		r0 = rf(ciPipelineIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(ciPipelineIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindById provides a mock function with given fields: id
func (_m *PipelineRepository) FindById(id int) (*pipelineConfig.Pipeline, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for FindById")
	}

	var r0 *pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*pipelineConfig.Pipeline, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) *pipelineConfig.Pipeline); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByIdEvenIfInactive provides a mock function with given fields: id
func (_m *PipelineRepository) FindByIdEvenIfInactive(id int) (*pipelineConfig.Pipeline, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for FindByIdEvenIfInactive")
	}

	var r0 *pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*pipelineConfig.Pipeline, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) *pipelineConfig.Pipeline); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByIdsIn provides a mock function with given fields: ids
func (_m *PipelineRepository) FindByIdsIn(ids []int) ([]*pipelineConfig.Pipeline, error) {
	ret := _m.Called(ids)

	if len(ret) == 0 {
		panic("no return value specified for FindByIdsIn")
	}

	var r0 []*pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) ([]*pipelineConfig.Pipeline, error)); ok {
		return rf(ids)
	}
	if rf, ok := ret.Get(0).(func([]int) []*pipelineConfig.Pipeline); ok {
		r0 = rf(ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByIdsInAndEnvironment provides a mock function with given fields: ids, environmentId
func (_m *PipelineRepository) FindByIdsInAndEnvironment(ids []int, environmentId int) ([]*pipelineConfig.Pipeline, error) {
	ret := _m.Called(ids, environmentId)

	if len(ret) == 0 {
		panic("no return value specified for FindByIdsInAndEnvironment")
	}

	var r0 []*pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func([]int, int) ([]*pipelineConfig.Pipeline, error)); ok {
		return rf(ids, environmentId)
	}
	if rf, ok := ret.Get(0).(func([]int, int) []*pipelineConfig.Pipeline); ok {
		r0 = rf(ids, environmentId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func([]int, int) error); ok {
		r1 = rf(ids, environmentId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByName provides a mock function with given fields: pipelineName
func (_m *PipelineRepository) FindByName(pipelineName string) (*pipelineConfig.Pipeline, error) {
	ret := _m.Called(pipelineName)

	if len(ret) == 0 {
		panic("no return value specified for FindByName")
	}

	var r0 *pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*pipelineConfig.Pipeline, error)); ok {
		return rf(pipelineName)
	}
	if rf, ok := ret.Get(0).(func(string) *pipelineConfig.Pipeline); ok {
		r0 = rf(pipelineName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(pipelineName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByParentCiPipelineId provides a mock function with given fields: ciPipelineId
func (_m *PipelineRepository) FindByParentCiPipelineId(ciPipelineId int) ([]*pipelineConfig.Pipeline, error) {
	ret := _m.Called(ciPipelineId)

	if len(ret) == 0 {
		panic("no return value specified for FindByParentCiPipelineId")
	}

	var r0 []*pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]*pipelineConfig.Pipeline, error)); ok {
		return rf(ciPipelineId)
	}
	if rf, ok := ret.Get(0).(func(int) []*pipelineConfig.Pipeline); ok {
		r0 = rf(ciPipelineId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(ciPipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByPipelineTriggerGitHash provides a mock function with given fields: gitHash
func (_m *PipelineRepository) FindByPipelineTriggerGitHash(gitHash string) (*pipelineConfig.Pipeline, error) {
	ret := _m.Called(gitHash)

	if len(ret) == 0 {
		panic("no return value specified for FindByPipelineTriggerGitHash")
	}

	var r0 *pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*pipelineConfig.Pipeline, error)); ok {
		return rf(gitHash)
	}
	if rf, ok := ret.Get(0).(func(string) *pipelineConfig.Pipeline); ok {
		r0 = rf(gitHash)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(gitHash)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindDeploymentAppTypeByAppIdAndEnvId provides a mock function with given fields: appId, envId
func (_m *PipelineRepository) FindDeploymentAppTypeByAppIdAndEnvId(appId int, envId int) (string, error) {
	ret := _m.Called(appId, envId)

	if len(ret) == 0 {
		panic("no return value specified for FindDeploymentAppTypeByAppIdAndEnvId")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(int, int) (string, error)); ok {
		return rf(appId, envId)
	}
	if rf, ok := ret.Get(0).(func(int, int) string); ok {
		r0 = rf(appId, envId)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(int, int) error); ok {
		r1 = rf(appId, envId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindDeploymentAppTypeByIds provides a mock function with given fields: ids
func (_m *PipelineRepository) FindDeploymentAppTypeByIds(ids []int) ([]*pipelineConfig.Pipeline, error) {
	ret := _m.Called(ids)

	if len(ret) == 0 {
		panic("no return value specified for FindDeploymentAppTypeByIds")
	}

	var r0 []*pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) ([]*pipelineConfig.Pipeline, error)); ok {
		return rf(ids)
	}
	if rf, ok := ret.Get(0).(func([]int) []*pipelineConfig.Pipeline); ok {
		r0 = rf(ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindDeploymentTypeByPipelineIds provides a mock function with given fields: cdPipelineIds
func (_m *PipelineRepository) FindDeploymentTypeByPipelineIds(cdPipelineIds []int) (map[int]pipelineConfig.DeploymentObject, error) {
	ret := _m.Called(cdPipelineIds)

	if len(ret) == 0 {
		panic("no return value specified for FindDeploymentTypeByPipelineIds")
	}

	var r0 map[int]pipelineConfig.DeploymentObject
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) (map[int]pipelineConfig.DeploymentObject, error)); ok {
		return rf(cdPipelineIds)
	}
	if rf, ok := ret.Get(0).(func([]int) map[int]pipelineConfig.DeploymentObject); ok {
		r0 = rf(cdPipelineIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[int]pipelineConfig.DeploymentObject)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(cdPipelineIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindIdsByAppIdsAndEnvironmentIds provides a mock function with given fields: appIds, environmentIds
func (_m *PipelineRepository) FindIdsByAppIdsAndEnvironmentIds(appIds []int, environmentIds []int) ([]int, error) {
	ret := _m.Called(appIds, environmentIds)

	if len(ret) == 0 {
		panic("no return value specified for FindIdsByAppIdsAndEnvironmentIds")
	}

	var r0 []int
	var r1 error
	if rf, ok := ret.Get(0).(func([]int, []int) ([]int, error)); ok {
		return rf(appIds, environmentIds)
	}
	if rf, ok := ret.Get(0).(func([]int, []int) []int); ok {
		r0 = rf(appIds, environmentIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]int)
		}
	}

	if rf, ok := ret.Get(1).(func([]int, []int) error); ok {
		r1 = rf(appIds, environmentIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindIdsByProjectIdsAndEnvironmentIds provides a mock function with given fields: projectIds, environmentIds
func (_m *PipelineRepository) FindIdsByProjectIdsAndEnvironmentIds(projectIds []int, environmentIds []int) ([]int, error) {
	ret := _m.Called(projectIds, environmentIds)

	if len(ret) == 0 {
		panic("no return value specified for FindIdsByProjectIdsAndEnvironmentIds")
	}

	var r0 []int
	var r1 error
	if rf, ok := ret.Get(0).(func([]int, []int) ([]int, error)); ok {
		return rf(projectIds, environmentIds)
	}
	if rf, ok := ret.Get(0).(func([]int, []int) []int); ok {
		r0 = rf(projectIds, environmentIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]int)
		}
	}

	if rf, ok := ret.Get(1).(func([]int, []int) error); ok {
		r1 = rf(projectIds, environmentIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindNumberOfAppsWithCdPipeline provides a mock function with given fields: appIds
func (_m *PipelineRepository) FindNumberOfAppsWithCdPipeline(appIds []int) (int, error) {
	ret := _m.Called(appIds)

	if len(ret) == 0 {
		panic("no return value specified for FindNumberOfAppsWithCdPipeline")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) (int, error)); ok {
		return rf(appIds)
	}
	if rf, ok := ret.Get(0).(func([]int) int); ok {
		r0 = rf(appIds)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(appIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindWithEnvironmentByCiIds provides a mock function with given fields: ctx, cIPipelineIds
func (_m *PipelineRepository) FindWithEnvironmentByCiIds(ctx context.Context, cIPipelineIds []int) ([]*pipelineConfig.Pipeline, error) {
	ret := _m.Called(ctx, cIPipelineIds)

	if len(ret) == 0 {
		panic("no return value specified for FindWithEnvironmentByCiIds")
	}

	var r0 []*pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []int) ([]*pipelineConfig.Pipeline, error)); ok {
		return rf(ctx, cIPipelineIds)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int) []*pipelineConfig.Pipeline); ok {
		r0 = rf(ctx, cIPipelineIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int) error); ok {
		r1 = rf(ctx, cIPipelineIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAppAndEnvDetailsForDeploymentAppTypePipeline provides a mock function with given fields: deploymentAppType, clusterIds
func (_m *PipelineRepository) GetAppAndEnvDetailsForDeploymentAppTypePipeline(deploymentAppType string, clusterIds []int) ([]*pipelineConfig.Pipeline, error) {
	ret := _m.Called(deploymentAppType, clusterIds)

	if len(ret) == 0 {
		panic("no return value specified for GetAppAndEnvDetailsForDeploymentAppTypePipeline")
	}

	var r0 []*pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(string, []int) ([]*pipelineConfig.Pipeline, error)); ok {
		return rf(deploymentAppType, clusterIds)
	}
	if rf, ok := ret.Get(0).(func(string, []int) []*pipelineConfig.Pipeline); ok {
		r0 = rf(deploymentAppType, clusterIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(string, []int) error); ok {
		r1 = rf(deploymentAppType, clusterIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetArgoPipelineByArgoAppName provides a mock function with given fields: argoAppName
func (_m *PipelineRepository) GetArgoPipelineByArgoAppName(argoAppName string) (pipelineConfig.Pipeline, error) {
	ret := _m.Called(argoAppName)

	if len(ret) == 0 {
		panic("no return value specified for GetArgoPipelineByArgoAppName")
	}

	var r0 pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (pipelineConfig.Pipeline, error)); ok {
		return rf(argoAppName)
	}
	if rf, ok := ret.Get(0).(func(string) pipelineConfig.Pipeline); ok {
		r0 = rf(argoAppName)
	} else {
		r0 = ret.Get(0).(pipelineConfig.Pipeline)
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(argoAppName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetArgoPipelinesHavingLatestTriggerStuckInNonTerminalStatuses provides a mock function with given fields: deployedBeforeMinutes, getPipelineDeployedWithinHours
func (_m *PipelineRepository) GetArgoPipelinesHavingLatestTriggerStuckInNonTerminalStatuses(deployedBeforeMinutes int, getPipelineDeployedWithinHours int) ([]*pipelineConfig.Pipeline, error) {
	ret := _m.Called(deployedBeforeMinutes, getPipelineDeployedWithinHours)

	if len(ret) == 0 {
		panic("no return value specified for GetArgoPipelinesHavingLatestTriggerStuckInNonTerminalStatuses")
	}

	var r0 []*pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int, int) ([]*pipelineConfig.Pipeline, error)); ok {
		return rf(deployedBeforeMinutes, getPipelineDeployedWithinHours)
	}
	if rf, ok := ret.Get(0).(func(int, int) []*pipelineConfig.Pipeline); ok {
		r0 = rf(deployedBeforeMinutes, getPipelineDeployedWithinHours)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int, int) error); ok {
		r1 = rf(deployedBeforeMinutes, getPipelineDeployedWithinHours)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetArgoPipelinesHavingTriggersStuckInLastPossibleNonTerminalTimelines provides a mock function with given fields: pendingSinceSeconds, timeForDegradation
func (_m *PipelineRepository) GetArgoPipelinesHavingTriggersStuckInLastPossibleNonTerminalTimelines(pendingSinceSeconds int, timeForDegradation int) ([]*pipelineConfig.Pipeline, error) {
	ret := _m.Called(pendingSinceSeconds, timeForDegradation)

	if len(ret) == 0 {
		panic("no return value specified for GetArgoPipelinesHavingTriggersStuckInLastPossibleNonTerminalTimelines")
	}

	var r0 []*pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int, int) ([]*pipelineConfig.Pipeline, error)); ok {
		return rf(pendingSinceSeconds, timeForDegradation)
	}
	if rf, ok := ret.Get(0).(func(int, int) []*pipelineConfig.Pipeline); ok {
		r0 = rf(pendingSinceSeconds, timeForDegradation)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int, int) error); ok {
		r1 = rf(pendingSinceSeconds, timeForDegradation)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByEnvOverrideId provides a mock function with given fields: envOverrideId
func (_m *PipelineRepository) GetByEnvOverrideId(envOverrideId int) ([]pipelineConfig.Pipeline, error) {
	ret := _m.Called(envOverrideId)

	if len(ret) == 0 {
		panic("no return value specified for GetByEnvOverrideId")
	}

	var r0 []pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]pipelineConfig.Pipeline, error)); ok {
		return rf(envOverrideId)
	}
	if rf, ok := ret.Get(0).(func(int) []pipelineConfig.Pipeline); ok {
		r0 = rf(envOverrideId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(envOverrideId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByEnvOverrideIdAndEnvId provides a mock function with given fields: envOverrideId, envId
func (_m *PipelineRepository) GetByEnvOverrideIdAndEnvId(envOverrideId int, envId int) (pipelineConfig.Pipeline, error) {
	ret := _m.Called(envOverrideId, envId)

	if len(ret) == 0 {
		panic("no return value specified for GetByEnvOverrideIdAndEnvId")
	}

	var r0 pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int, int) (pipelineConfig.Pipeline, error)); ok {
		return rf(envOverrideId, envId)
	}
	if rf, ok := ret.Get(0).(func(int, int) pipelineConfig.Pipeline); ok {
		r0 = rf(envOverrideId, envId)
	} else {
		r0 = ret.Get(0).(pipelineConfig.Pipeline)
	}

	if rf, ok := ret.Get(1).(func(int, int) error); ok {
		r1 = rf(envOverrideId, envId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetConnection provides a mock function with given fields:
func (_m *PipelineRepository) GetConnection() *pg.DB {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetConnection")
	}

	var r0 *pg.DB
	if rf, ok := ret.Get(0).(func() *pg.DB); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pg.DB)
		}
	}

	return r0
}

// GetPostStageConfigById provides a mock function with given fields: id
func (_m *PipelineRepository) GetPostStageConfigById(id int) (*pipelineConfig.Pipeline, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for GetPostStageConfigById")
	}

	var r0 *pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*pipelineConfig.Pipeline, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) *pipelineConfig.Pipeline); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MarkPartiallyDeleted provides a mock function with given fields: id, userId, tx
func (_m *PipelineRepository) MarkPartiallyDeleted(id int, userId int32, tx *pg.Tx) error {
	ret := _m.Called(id, userId, tx)

	if len(ret) == 0 {
		panic("no return value specified for MarkPartiallyDeleted")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int, int32, *pg.Tx) error); ok {
		r0 = rf(id, userId, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// PipelineExists provides a mock function with given fields: pipelineName
func (_m *PipelineRepository) PipelineExists(pipelineName string) (bool, error) {
	ret := _m.Called(pipelineName)

	if len(ret) == 0 {
		panic("no return value specified for PipelineExists")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (bool, error)); ok {
		return rf(pipelineName)
	}
	if rf, ok := ret.Get(0).(func(string) bool); ok {
		r0 = rf(pipelineName)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(pipelineName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Save provides a mock function with given fields: pipeline, tx
func (_m *PipelineRepository) Save(pipeline []*pipelineConfig.Pipeline, tx *pg.Tx) error {
	ret := _m.Called(pipeline, tx)

	if len(ret) == 0 {
		panic("no return value specified for Save")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func([]*pipelineConfig.Pipeline, *pg.Tx) error); ok {
		r0 = rf(pipeline, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SetDeploymentAppCreatedInPipeline provides a mock function with given fields: deploymentAppCreated, pipelineId, userId
func (_m *PipelineRepository) SetDeploymentAppCreatedInPipeline(deploymentAppCreated bool, pipelineId int, userId int32) error {
	ret := _m.Called(deploymentAppCreated, pipelineId, userId)

	if len(ret) == 0 {
		panic("no return value specified for SetDeploymentAppCreatedInPipeline")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(bool, int, int32) error); ok {
		r0 = rf(deploymentAppCreated, pipelineId, userId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UniqueAppEnvironmentPipelines provides a mock function with given fields:
func (_m *PipelineRepository) UniqueAppEnvironmentPipelines() ([]*pipelineConfig.Pipeline, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for UniqueAppEnvironmentPipelines")
	}

	var r0 []*pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]*pipelineConfig.Pipeline, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []*pipelineConfig.Pipeline); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Update provides a mock function with given fields: pipeline, tx
func (_m *PipelineRepository) Update(pipeline *pipelineConfig.Pipeline, tx *pg.Tx) error {
	ret := _m.Called(pipeline, tx)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*pipelineConfig.Pipeline, *pg.Tx) error); ok {
		r0 = rf(pipeline, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateCdPipelineAfterDeployment provides a mock function with given fields: deploymentAppType, cdPipelineIdIncludes, userId, delete
func (_m *PipelineRepository) UpdateCdPipelineAfterDeployment(deploymentAppType string, cdPipelineIdIncludes []int, userId int32, delete bool) error {
	ret := _m.Called(deploymentAppType, cdPipelineIdIncludes, userId, delete)

	if len(ret) == 0 {
		panic("no return value specified for UpdateCdPipelineAfterDeployment")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string, []int, int32, bool) error); ok {
		r0 = rf(deploymentAppType, cdPipelineIdIncludes, userId, delete)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateCdPipelineDeploymentAppInFilter provides a mock function with given fields: deploymentAppType, cdPipelineIdIncludes, userId, deploymentAppCreated, delete
func (_m *PipelineRepository) UpdateCdPipelineDeploymentAppInFilter(deploymentAppType string, cdPipelineIdIncludes []int, userId int32, deploymentAppCreated bool, delete bool) error {
	ret := _m.Called(deploymentAppType, cdPipelineIdIncludes, userId, deploymentAppCreated, delete)

	if len(ret) == 0 {
		panic("no return value specified for UpdateCdPipelineDeploymentAppInFilter")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string, []int, int32, bool, bool) error); ok {
		r0 = rf(deploymentAppType, cdPipelineIdIncludes, userId, deploymentAppCreated, delete)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateCiPipelineId provides a mock function with given fields: tx, pipelineIds, ciPipelineId
func (_m *PipelineRepository) UpdateCiPipelineId(tx *pg.Tx, pipelineIds []int, ciPipelineId int) error {
	ret := _m.Called(tx, pipelineIds, ciPipelineId)

	if len(ret) == 0 {
		panic("no return value specified for UpdateCiPipelineId")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*pg.Tx, []int, int) error); ok {
		r0 = rf(tx, pipelineIds, ciPipelineId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateOldCiPipelineIdToNewCiPipelineId provides a mock function with given fields: tx, oldCiPipelineId, newCiPipelineId
func (_m *PipelineRepository) UpdateOldCiPipelineIdToNewCiPipelineId(tx *pg.Tx, oldCiPipelineId int, newCiPipelineId int) error {
	ret := _m.Called(tx, oldCiPipelineId, newCiPipelineId)

	if len(ret) == 0 {
		panic("no return value specified for UpdateOldCiPipelineIdToNewCiPipelineId")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*pg.Tx, int, int) error); ok {
		r0 = rf(tx, oldCiPipelineId, newCiPipelineId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewPipelineRepository creates a new instance of PipelineRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewPipelineRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *PipelineRepository {
	mock := &PipelineRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
