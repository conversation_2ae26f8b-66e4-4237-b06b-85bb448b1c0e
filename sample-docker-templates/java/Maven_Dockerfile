################################# Build Container ###############################

# Use latest Maven with Amazon Corretto 21 on Debian for consistent build environment
FROM maven:3.9.9-amazoncorretto-21-debian as base

# Set working directory inside container
WORKDIR /build

# Copy pom.xml separately to leverage Docker cache for dependencies
COPY pom.xml .

# Download dependencies for offline use
RUN mvn dependency:go-offline

# Copy the source code to container
COPY src/ /build/src/

# Build the project and package the application
RUN mvn clean package

################################# Prod Container #################################

# Use a slim OpenJDK 21 image based on Debian for production
FROM eclipse-temurin:21-jdk-jammy

# Create a non-root user 'nonroot' for security best practices
RUN addgroup --gid 2002 nonroot && \
    adduser --gid 2002 --uid 2002 nonroot --disabled-password --gecos ""

# Set working directory
WORKDIR /app

# Copy the built jar from build stage
COPY --from=base /build/target/*.jar /app/demo.jar

# Change ownership to non-root user
RUN chown nonroot:nonroot /app/demo.jar

# Switch to non-root user
USER nonroot

# Expose the port the app listens on
EXPOSE 8080

# Default command to run the jar file
CMD ["java", "-jar", "/app/demo.jar"]
