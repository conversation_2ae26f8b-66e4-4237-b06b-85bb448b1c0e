worker_processes auto;
error_log  /tmp/nginx-logs/error.log warn;
pid        /tmp/nginx-logs/nginx.pid;

events {}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    access_log  /tmp/nginx-logs/access.log;

    client_body_temp_path /tmp/nginx-logs/client_temp;
    proxy_temp_path       /tmp/nginx-logs/proxy_temp;
    fastcgi_temp_path     /tmp/nginx-logs/fastcgi_temp;
    uwsgi_temp_path       /tmp/nginx-logs/uwsgi_temp;
    scgi_temp_path        /tmp/nginx-logs/scgi_temp;

    server {
        listen 8080;
        server_name  localhost;

        location / {
            proxy_pass http://127.0.0.1:5000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /static/ {
            alias /app/static/;
        }
    }
}
