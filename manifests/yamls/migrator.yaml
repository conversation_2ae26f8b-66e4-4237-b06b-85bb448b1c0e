# Source: postgresql/templates/secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: postgresql-migrator
  labels:
    app: postgresql
    chart: postgresql-8.6.4
    release: "devtron"
type: Opaque
---
apiVersion: batch/v1
kind: Job
metadata:
  name: postgresql-migrate-devtron
spec:
  template:
    spec:
      securityContext:
        fsGroup: 1000
        runAsGroup: 1000
        runAsUser: 1000
      containers:
      - name: postgresql-migrate-devtron
        image: quay.io/devtron/migrator:ec1dcab8-149-13278
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 1000
          runAsNonRoot: true
        env:
        - name: GIT_BRANCH
          value: main
        - name: SCRIPT_LOCATION
          value: scripts/sql/
        - name: GIT_REPO_URL
          value: https://github.com/devtron-labs/devtron.git
        - name: DB_TYPE
          value: postgres
        - name: DB_USER_NAME
          value: postgres
        - name: DB_HOST
          value: postgresql-postgresql.devtroncd
        - name: DB_PORT
          value: "5432"
        - name: DB_NAME
          value: orchestrator                      
        - name: MIGRATE_TO_VERSION
          value: "0"
        - name: GIT_HASH
          value: 1e666e4747c25babd0d027f26844692fdeba5e61
        envFrom:
          - secretRef:
              name: postgresql-migrator
      restartPolicy: OnFailure
  backoffLimit: 20
  activeDeadlineSeconds: 1500
---
apiVersion: batch/v1
kind: Job
metadata:
  name: postgresql-migrate-casbin
spec:
  template:
    spec:
      securityContext:
        fsGroup: 1000
        runAsGroup: 1000
        runAsUser: 1000
      serviceAccountName: devtron
      containers:
      - name: devtron-rollout
        image: "quay.io/devtron/kubectl:latest"
        command: ['sh', '-c', 'kubectl rollout restart deployment/devtron -n devtroncd && kubectl rollout restart deployment/kubelink -n devtroncd']
      initContainers:
      - name: postgresql-migrate-casbin
        image: quay.io/devtron/migrator:ec1dcab8-149-13278
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 1000
          runAsNonRoot: true
        env:
        - name: SCRIPT_LOCATION
          value: scripts/casbin/
        - name: GIT_REPO_URL
          value: https://github.com/devtron-labs/devtron.git
        - name: DB_TYPE
          value: postgres
        - name: DB_USER_NAME
          value: postgres
        - name: DB_HOST
          value: postgresql-postgresql.devtroncd
        - name: DB_PORT
          value: "5432"
        - name: DB_NAME
          value: casbin
        - name: MIGRATE_TO_VERSION
          value: "0"
        - name: GIT_HASH
          value: 1e666e4747c25babd0d027f26844692fdeba5e61
        - name: GIT_BRANCH
          value: main
        envFrom:
          - secretRef:
              name: postgresql-migrator
        resources:
          requests:
            cpu: 0.5
            memory: 500Mi
      restartPolicy: OnFailure
  backoffLimit: 20
  activeDeadlineSeconds: 1500
---
apiVersion: batch/v1
kind: Job
metadata:
  name: postgresql-migrate-gitsensor
spec:
  template:
    spec:
      securityContext:
        fsGroup: 1000
        runAsGroup: 1000
        runAsUser: 1000
      containers:
      - name: postgresql-migrate-gitsensor
        image: quay.io/devtron/migrator:ec1dcab8-149-13278
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 1000
          runAsNonRoot: true
        env:
        - name: SCRIPT_LOCATION
          value: scripts/sql/
        - name: GIT_REPO_URL
          value: https://github.com/devtron-labs/git-sensor.git
        - name: DB_TYPE
          value: postgres
        - name: DB_USER_NAME
          value: postgres
        - name: DB_HOST
          value: postgresql-postgresql.devtroncd
        - name: DB_PORT
          value: "5432"
        - name: DB_NAME
          value: git_sensor
        - name: MIGRATE_TO_VERSION
          value: "0"
        - name: GIT_BRANCH
          value: main
        - name: GIT_HASH
          value: 86e132830b6196e714657cf84492728d52828f22
        envFrom:
          - secretRef:
              name: postgresql-migrator
      restartPolicy: OnFailure
  backoffLimit: 20
  activeDeadlineSeconds: 1500
---
apiVersion: batch/v1
kind: Job
metadata:
  name: postgresql-migrate-lens
spec:
  template:
    spec:
      securityContext:
        fsGroup: 1000
        runAsGroup: 1000
        runAsUser: 1000
      containers:
      - name: postgresql-migrate-lens
        image: quay.io/devtron/migrator:ec1dcab8-149-13278
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 1000
          runAsNonRoot: true
        env:
        - name: SCRIPT_LOCATION
          value: scripts/sql/
        - name: GIT_REPO_URL
          value: https://github.com/devtron-labs/lens.git
        - name: DB_TYPE
          value: postgres
        - name: DB_USER_NAME
          value: postgres
        - name: DB_HOST
          value: postgresql-postgresql.devtroncd
        - name: DB_PORT
          value: "5432"
        - name: DB_NAME
          value: lens
        - name: MIGRATE_TO_VERSION
          value: "0"
        - name: GIT_BRANCH
          value: main
        - name: GIT_HASH
          value: 70577aaa1ad185d4e145999bccf072144647eddc
        envFrom:
          - secretRef:
              name: postgresql-migrator
      restartPolicy: OnFailure
  backoffLimit: 20
  activeDeadlineSeconds: 1500
---
apiVersion: batch/v1
#this job is added for creating new database(clairv4).
#This database is needed for clair upgrade (v2 to v4), since old database does not support migration for new clair.
#Without this job, database can be created for new users, but not for existing users.
kind: Job
metadata:
  name: postgresql-miscellaneous
spec:
  ttlSecondsAfterFinished: 1000
  template:
    spec:
      securityContext:
        fsGroup: 1000
        runAsGroup: 1000
        runAsUser: 1000
      containers:
      - name: postgresql-miscellaneous
        image: quay.io/devtron/postgres:11.9
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 1000
          runAsNonRoot: true
        env:
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: postgresql-postgresql
              key: postgresql-password
        - name: PGHOST
          value: postgresql-postgresql
        command:
          - /bin/sh
          - -c
          - psql -Upostgres -f /docker-entrypoint-initdb.d/db_create.sql
        volumeMounts:
          - name: custom-init-scripts
            mountPath: /docker-entrypoint-initdb.d/
      volumes:
        - name: custom-init-scripts
          configMap:
            name: postgresql-postgresql-init-scripts
      restartPolicy: OnFailure
  backoffLimit: 20
  activeDeadlineSeconds: 1500
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: app-sync-cronjob
spec:
  schedule: "0 19 * * *"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: chart-sync
            image: quay.io/devtron/chart-sync:d0dcc590-373-21074
            env:
            - name: PG_ADDR
              value: postgresql-postgresql.devtroncd
            - name: PG_DATABASE
              value: orchestrator
            - name: PG_USER
              value: postgres
            envFrom:
            - secretRef:
                name: devtron-secret
          restartPolicy: Never
      backoffLimit: 4
