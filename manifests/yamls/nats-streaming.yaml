---
# Source: stan/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: devtron-stan-config
  labels:
    app: devtron-stan
    chart: stan-0.9.2
data:
  stan.conf: |-
    #########################
    # NATS Streaming Config #
    #########################
    streaming {
      ns: nats://devtron-nats:4222

      id: devtron-stan

      ###############################
      #  Store Config               #
      ###############################
      store: "file"
      dir: /data/stan/store
      partitioning: false
    }

    ###############################################
    #                                             #
    #            Embedded NATS Config             #
    #                                             #
    ###############################################


    # PID file shared with configuration reloader.
    pid_file: "/var/run/stan/stan.pid"

    ###############
    #             #
    # Monitoring  #
    #             #
    ###############
    http: 8222
    server_name: $POD_NAME
---
# Source: stan/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: devtron-stan
  labels:
    app: devtron-stan
    chart: stan-0.9.2
spec:
  selector:
    app: devtron-stan
  clusterIP: None
  ports:
  - name: metrics
    port: 7777
  - name: monitor
    port: 8222
---
# Source: stan/templates/statefulset.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: devtron-stan
  labels:
    app: devtron-stan
    chart: stan-0.9.2
spec:
  selector:
    matchLabels:
      app: devtron-stan

  replicas: 1

  # NATS Streaming service name
  serviceName: devtron-stan

  template:
    metadata:
      annotations:
        prometheus.io/path: /metrics
        prometheus.io/port: "7777"
        prometheus.io/scrape: "true"
      labels:
        app: devtron-stan
        chart: stan-0.9.2
    spec:
      terminationGracePeriodSeconds: 30
      volumes:
      - configMap:
          name: devtron-stan-config
          defaultMode: 0755
        name: config-volume

      # Local volume shared with the reloader.
      - name: pid
        emptyDir: {}

      affinity:
        podAntiAffinity:
#          requiredDuringSchedulingIgnoredDuringExecution:
#          - labelSelector:
#              matchExpressions:
#              - key: app
#                operator: In
#                values:
#                - devtron-stan
#            topologyKey: kubernetes.io/hostname
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - devtron-stan
                topologyKey: kubernetes.io/hostname
      containers:
        ####################
        #  NATS Streaming  #
        ####################
        - name: stan
          image: quay.io/devtron/nats-streaming:0.23.0
          args:
          - -sc
          - /etc/stan-config/stan.conf
          env:
          - name: POD_NAME
            valueFrom:
              fieldRef:
                fieldPath: metadata.name
          - name: POD_NAMESPACE
            valueFrom:
              fieldRef:
                fieldPath: metadata.namespace
          - name: CLUSTER_ADVERTISE
            value: $(POD_NAME).devtron-stan.$(POD_NAMESPACE).svc
          - name: STAN_SERVICE_NAME
            value: devtron-stan
          - name: STAN_REPLICAS
            value: "1"
          ports:
          - containerPort: 8222
            name: monitor
          - containerPort: 7777
            name: metrics
          readinessProbe:
            httpGet:
              path: /streaming/serverz
              port: monitor
            timeoutSeconds: 2

          volumeMounts:
          - name: config-volume
            mountPath: /etc/stan-config
          - name: devtron-stan-pvc
            mountPath: /data/stan
          - name: pid
            mountPath: /var/run/stan
        #################################
        #                               #
        #  NATS Prometheus Exporter     #
        #                               #
        #################################
        - name: metrics
          image: quay.io/devtron/prometheus-nats-exporter:latest
          args:
          - -connz
          - -routez
          - -subz
          - -varz
          - -channelz
          - -serverz
          - http://localhost:8222/
          ports:
          - containerPort: 7777
            name: metrics
  volumeClaimTemplates:
  - metadata:
      name: devtron-stan-pvc
    spec:
      accessModes:
      - ReadWriteOnce
      resources:
        requests:
          storage: 1Gi
