// Code generated by mockery v2.14.0. DO NOT EDIT.

package mocks

import (
	chartConfig "github.com/devtron-labs/devtron/internal/sql/repository/chartConfig"
	chartRepoRepository "github.com/devtron-labs/devtron/pkg/chartRepo/repository"

	mock "github.com/stretchr/testify/mock"

	pg "github.com/go-pg/pg"
)

// PipelineConfigRepository is an autogenerated mock type for the PipelineConfigRepository type
type PipelineConfigRepository struct {
	mock.Mock
}

// MarkAsDeleted provides a mock function with given fields: pipelineStrategy, tx
func (_m *PipelineConfigRepository) MarkAsDeleted(pipelineStrategy *chartConfig.PipelineStrategy, userId int32, tx *pg.Tx) error {
	ret := _m.Called(pipelineStrategy, tx)

	var r0 error
	if rf, ok := ret.Get(0).(func(*chartConfig.PipelineStrategy, *pg.Tx) error); ok {
		r0 = rf(pipelineStrategy, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// FindById provides a mock function with given fields: id
func (_m *PipelineConfigRepository) FindById(id int) (*chartConfig.PipelineStrategy, error) {
	ret := _m.Called(id)

	var r0 *chartConfig.PipelineStrategy
	if rf, ok := ret.Get(0).(func(int) *chartConfig.PipelineStrategy); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*chartConfig.PipelineStrategy)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByStrategy provides a mock function with given fields: strategy
func (_m *PipelineConfigRepository) FindByStrategy(strategy chartRepoRepository.DeploymentStrategy) (*chartConfig.PipelineStrategy, error) {
	ret := _m.Called(strategy)

	var r0 *chartConfig.PipelineStrategy
	if rf, ok := ret.Get(0).(func(chartRepoRepository.DeploymentStrategy) *chartConfig.PipelineStrategy); ok {
		r0 = rf(strategy)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*chartConfig.PipelineStrategy)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(chartRepoRepository.DeploymentStrategy) error); ok {
		r1 = rf(strategy)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByStrategyAndPipelineId provides a mock function with given fields: strategy, pipelineId
func (_m *PipelineConfigRepository) FindByStrategyAndPipelineId(strategy chartRepoRepository.DeploymentStrategy, pipelineId int) (*chartConfig.PipelineStrategy, error) {
	ret := _m.Called(strategy, pipelineId)

	var r0 *chartConfig.PipelineStrategy
	if rf, ok := ret.Get(0).(func(chartRepoRepository.DeploymentStrategy, int) *chartConfig.PipelineStrategy); ok {
		r0 = rf(strategy, pipelineId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*chartConfig.PipelineStrategy)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(chartRepoRepository.DeploymentStrategy, int) error); ok {
		r1 = rf(strategy, pipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllStrategyByPipelineId provides a mock function with given fields: pipelineId
func (_m *PipelineConfigRepository) GetAllStrategyByPipelineId(pipelineId int) ([]*chartConfig.PipelineStrategy, error) {
	ret := _m.Called(pipelineId)

	var r0 []*chartConfig.PipelineStrategy
	if rf, ok := ret.Get(0).(func(int) []*chartConfig.PipelineStrategy); ok {
		r0 = rf(pipelineId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*chartConfig.PipelineStrategy)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(pipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllStrategyByPipelineIds provides a mock function with given fields: pipelineIds
func (_m *PipelineConfigRepository) GetAllStrategyByPipelineIds(pipelineIds []int) ([]*chartConfig.PipelineStrategy, error) {
	ret := _m.Called(pipelineIds)

	var r0 []*chartConfig.PipelineStrategy
	if rf, ok := ret.Get(0).(func([]int) []*chartConfig.PipelineStrategy); ok {
		r0 = rf(pipelineIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*chartConfig.PipelineStrategy)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(pipelineIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDefaultStrategyByPipelineId provides a mock function with given fields: pipelineId
func (_m *PipelineConfigRepository) GetDefaultStrategyByPipelineId(pipelineId int) (*chartConfig.PipelineStrategy, error) {
	ret := _m.Called(pipelineId)

	var r0 *chartConfig.PipelineStrategy
	if rf, ok := ret.Get(0).(func(int) *chartConfig.PipelineStrategy); ok {
		r0 = rf(pipelineId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*chartConfig.PipelineStrategy)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(pipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Save provides a mock function with given fields: pipelineStrategy, tx
func (_m *PipelineConfigRepository) Save(pipelineStrategy *chartConfig.PipelineStrategy, tx *pg.Tx) error {
	ret := _m.Called(pipelineStrategy, tx)

	var r0 error
	if rf, ok := ret.Get(0).(func(*chartConfig.PipelineStrategy, *pg.Tx) error); ok {
		r0 = rf(pipelineStrategy, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Update provides a mock function with given fields: pipelineStrategy, tx
func (_m *PipelineConfigRepository) Update(pipelineStrategy *chartConfig.PipelineStrategy, tx *pg.Tx) error {
	ret := _m.Called(pipelineStrategy, tx)

	var r0 error
	if rf, ok := ret.Get(0).(func(*chartConfig.PipelineStrategy, *pg.Tx) error); ok {
		r0 = rf(pipelineStrategy, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewPipelineConfigRepository interface {
	mock.TestingT
	Cleanup(func())
}

// NewPipelineConfigRepository creates a new instance of PipelineConfigRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewPipelineConfigRepository(t mockConstructorTestingTNewPipelineConfigRepository) *PipelineConfigRepository {
	mock := &PipelineConfigRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
