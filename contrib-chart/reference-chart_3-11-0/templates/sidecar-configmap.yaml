{{- if .Values.appMetrics }}
apiVersion: v1
kind: ConfigMap
metadata:
  creationTimestamp: 2019-08-12T18:38:34Z
  name: sidecar-config-{{ template ".Chart.Name .name" $ }}
data:
  envoy-config.json: |
    {
      "stats_config": {
        "use_all_default_tags": false,
        "stats_tags": [
          {
            "tag_name": "cluster_name",
            "regex": "^cluster\\.((.+?(\\..+?\\.svc\\.cluster\\.local)?)\\.)"
          },
          {
            "tag_name": "tcp_prefix",
            "regex": "^tcp\\.((.*?)\\.)\\w+?$"
          },
          {
            "tag_name": "response_code",
            "regex": "_rq(_(\\d{3}))$"
          },
          {
            "tag_name": "response_code_class",
            "regex": ".*_rq(_(\\dxx))$"
          },
          {
            "tag_name": "http_conn_manager_listener_prefix",
            "regex": "^listener(?=\\.).*?\\.http\\.(((?:[_.[:digit:]]*|[_\\[\\]aAbBcCdDeEfF[:digit:]]*))\\.)"
          },
          {
            "tag_name": "http_conn_manager_prefix",
            "regex": "^http\\.(((?:[_.[:digit:]]*|[_\\[\\]aAbBcCdDeEfF[:digit:]]*))\\.)"
          },
          {
            "tag_name": "listener_address",
            "regex": "^listener\\.(((?:[_.[:digit:]]*|[_\\[\\]aAbBcCdDeEfF[:digit:]]*))\\.)"
          },
          {
            "tag_name": "mongo_prefix",
            "regex": "^mongo\\.(.+?)\\.(collection|cmd|cx_|op_|delays_|decoding_)(.*?)$"
          }
        ],
        "stats_matcher": {
          "inclusion_list": {
            "patterns": [
              {
              "regex": ".*_rq_\\dxx$"
              },
              {
              "regex": ".*_rq_time$"
              },
              {
              "regex": "cluster.*"
              },
            ]
          }
        }
      },
      "admin": {
        "access_log_path": "/dev/null",
        "address": {
          "socket_address": {
            "address": "0.0.0.0",
            "port_value": 9901
          }
        }
      },
      "static_resources": {
        "clusters": [
    {{- range $index, $element := .Values.ContainerPort }}
          {
            "name": "{{ $.Values.app }}-{{ $index }}",
            "type": "STATIC",
            "connect_timeout": "0.250s",
            "lb_policy": "ROUND_ROBIN",
{{- if $element.idleTimeout }}
            "common_http_protocol_options": {
              "idle_timeout": {{ $element.idleTimeout | quote }}
            },
{{- end }}
{{- if or $element.useHTTP2 $element.useGRPC }}
            "http2_protocol_options": {},
{{- end }}
            "load_assignment": {
              "cluster_name": "9",
              "endpoints": {
                "lb_endpoints": [
                {
                  "endpoint": {
                    "address": {
                      "socket_address": {
                        "protocol": "TCP",
                        "address": "127.0.0.1",
                        "port_value": {{ $element.port  }}
                      }
                    }
                  }
                }
                ]
              }
            }
          },
    {{- end }}
        ],
        "listeners":[
    {{- range $index, $element := .Values.ContainerPort }}
          {
            "address": {
              "socket_address": {
                "protocol": "TCP",
                "address": "0.0.0.0",
                "port_value": {{ $element.envoyPort | default (add 8790 $index) }}
              }
            },
            "filter_chains": [
              {
                "filters": [
                  {
                    "name": "envoy.filters.network.http_connection_manager",
                    "config": {
                      "codec_type": "AUTO",
                      "stat_prefix": "stats",
                      "route_config": {
                        "virtual_hosts": [
                          {
                            "name": "backend",
                            "domains": [
                              "*"
                            ],
                            "routes": [
                              {
                                "match": {
                                  "prefix": "/"
                                },
                                "route": {
{{- if $element.supportStreaming }}
                                  "timeout": "0s",
{{- end }}
                                  "cluster": "{{ $.Values.app }}-{{ $index }}"
                                }
                              }
                            ]
                          }
                        ]
                      },
                      "http_filters": {
                        "name": "envoy.filters.http.router"
                      }
                    }
                  }
                ]
              }
            ]
          },
    {{- end }}
        ]
      }
    }
---
{{- end }}
