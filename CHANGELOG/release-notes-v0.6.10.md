## v0.6.10

## Bugs
- fix: safe check added if cell value is not found in resource list (#2839)
## Enhancements
- feat: Resource viewer implementation for a cluster (#2811)
- feat: Deployment status restructuring (#2806)


## v0.6.10-rc.1

## Bugs
- fix: cluster name passed in node metadata list (#2804)
- fix: Chart with nested directory  (#2797)
## Enhancements
- feat: added support for node-[delete, cordon, drain, taint edit] (#2805)
- feat: Cluster terminal multiple image support (#2815)
## Documentation
- docs: minor correction in ingress yaml (#2819)
- docs: updated cluster server url (#2816)
- docs: minor updates (#2800)
- docs: updated-global-configs (#2712)
- docs: added deployment status shows failed or degraded in troubleshooting section (#2795)
- docs: uninstall minor updates (#2788)
- docs: Updated devtron admin password reset doc (#2785)


## v0.6.10-rc.0

## Bugs
- fix: Authenticator updated - handling api token (#2745)
- fix: Ci workflow status update when workflow stuck. (#2726)
- fix: Update Chart.yaml for security integration chart fix (#2771)
- fix: Helm app proxy chart auto fix (#2754)
- fix: No need to bounce orchestrator if SSO config are added/updated (#2753)
- fix: ArgoCD Connection Manager connection handling (#2702)
## Enhancements
- feat: added new chart for job and cronjob with keda scaledjob support (#2749)
## Documentation
- docs: deployment updates (#2769)
- docs: deployment template doc (#2770)
- docs: install command corrected (#2775)
- docs: broken links fixed in doc v0.6 (#2776)



