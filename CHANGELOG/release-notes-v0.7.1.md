## v0.7.1

## Bugs
- fix: EA mode wire fix (#5462)
- fix: compare manifest fixes (#5430)
- fix: override clusterRbac with direct allow behaviour for super admin (#5449)
- fix: external helm app when linked to devtron and page breaks while adding project to it, without switching back to applist  (#5443)
- fix: empty the code and image scan script (#5434)
- fix: K8s Resource list RBAC ignore for Superadmin (#5415)
- fix: repo url and name handling with argocd (#5445)
- fix: fix for terminal disconnect issue when custom transport is being used (#5436)
- fix: gitops async failed for git cli mode in concurrent cases  (#5412)
- fix: Updating pr-issue-validator-script (#5384)
- fix: optimised FetchLatestDeploymentWithChartRefs query (#5393)
- fix: nats consumer deleted on shutdown (#5377)
- fix: panic issue in get/ download pod logs api (#5342)
- fix: encountering panic in application groups in build and deploy page (#5330)
- fix: chart group rbac issue (#5183)
- fix: Multiple choice option for namespace in Kubernetes resource permission (#5293)
- fix: restart workloads fix in app group (#5313)
- fix: deployment chart fix (#5215)
- fix: docker file version fix (#5299)
- fix: hibernating status is not being updated in app listing page (#5294)
## Enhancements
- feat: Checking multiarchitecture of images (#5232)
- feat: updated kubelink grpc client cfg (#5426)
- feat: Integration of Cranecopy plugin (#5131)
- feat: casbin upgraded to v2 (#5329)
- feat: new scripts added for rescan sbom support , helm manifest scan flag and git container links  (#5406)
- feat: Reload materials api added (#5182)
- feat: mirgator plugin (#5347)
- feat: insecure support for chart-sync (#5328)
- feat: GitOps async install for devtron applications (#5169)
- feat: chart ref schema db migration (#5319)
- feat: Up and Down Script for BitBucket Plugin v1.0.0 (#4949)
- feat: Added statefulset chart 5.1.0 (#5199)
- feat: air gap registry v2 (#5220)
- feat: tenants and installations migration (#5187)
## Documentation
- doc: Blob Storage Redirection + Other Fixes (#5432)
- doc: Added migration steps for 0.6 to 0.7 upgrade (#5411)
- doc: Created Deployment Window Draft (#4800)
- doc: Redirection Fix for User Permissions Doc + Other Fixes (#5382)
- doc: Redirection Fixes for 0.7 (#5381)
- doc: Redirection Issue Trial Fix (#5378)
- doc: Plugin Creation Doc (#5372)
- docs: Added specs for the global plugin Apis (#5362)
- docs: Fixes +  Corrections in Docs (#5335)
- docs: fixed broken link in readme (#5337)
- docs: removed users (#5324)
- docs: Created a file for listing Devtron Users (#5310)
## Others
- chore: common-lib upgrade for nats replicas (#5446)
- chore: migration for gitops config (#5383)
- chore: update common-lib tag version (#5333)
- chore: updated go version in EA dockerfile (#5327)



