openapi: "3.0.3"
info:
  version: 1.0.0
  title: Devtron Labs
paths:
  /orchestrator/k8s/resource:
    post:
      description: this api will be used for fetching all kind of manifest.
      requestBody:
        description: json as request body
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceRequestObject'
      responses:
        "200":
          description: manifest fetch responces
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    $ref: '#/components/schemas/ResourceGetResponse'
    put:
      description: this api will be used for edit requested manifest.
      requestBody:
        description: json as request body
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceRequestObject'
      responses:
        "200":
          description: manifest edit responces
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    $ref: '#/components/schemas/ResourceGetResponse'
  /orchestrator/k8s/resource/create:
    post:
      description: this api will be used for applying desired manifest
      requestBody:
        description: json as request body
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceRequestObject'
      responses:
        "200":
          description: create resource response
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    $ref: '#/components/schemas/ResourceGetResponse'
  /orchestrator/k8s/resource/delete:
    post:
      description: this api will be used for delete any resource.
      requestBody:
        description: json as request body
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceRequestObject'
      responses:
        "200":
          description: manifest fetch responces
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    $ref: '#/components/schemas/ResourceGetResponse'
  /orchestrator/k8s/events:
    post:
      description: this api will be used for fetching events for resources.
      requestBody:
        required: false
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceRequestObject'
      responses:
        "200":
          description: events success
          content:
            text/event-stream:
              schema:
                $ref: "#/components/schemas/EventsResponseObject"
  /orchestrator/k8s/pods/logs/{podName}:
    get:
      description: this api will be used for fetching logs for container.
      parameters:
        - name: podName
          in: path
          required: true
          schema:
            type: string
        - name: containerName
          in: query
          required: true
          schema:
            type: string
        - name: appId
          in: query
          required: false
          schema:
            type: string
        - name: clusterId
          in: query
          required: false
          schema:
            type: integer
        - name: namespace
          in: query
          description: it is required when clusterId is passed
          required: false
          schema:
            type: string
        - name: follow
          in: query
          schema:
            type: boolean
        - name: sinceSeconds
          in: query
          schema:
            type: integer
        - name: tailLines
          in: query
          schema:
            type: integer
      responses:
        "200":
          description: events success
          content:
            text/event-stream:
              schema:
                $ref: "#/components/schemas/LogsResponseObject"
  /orchestrator/k8s/pod/exec/session/{identifier}/{namespace}/{pod}/{shell}/{container}:
    get:
      description: get session for the terminal
      parameters:
        - in: path
          name: identifier
          schema:
            type: string
          required: true
          description: application id or cluster id
          example: "2|devtroncd|devtron or 3"
        - in: path
          name: namespace
          schema:
            type: string
          required: true
          description: namespace name
          example: "devtroncd"
        - in: path
          name: pod
          schema:
            type: string
          required: true
          description: pod name
          example: inception-58d44d99fd-tfw4s
        - in: path
          name: shell
          schema:
            type: string
            oneOf:
              - "bash"
              - "sh"
              - "powershell"
              - "cmd"
          required: true
          description: shell to invoke
          example: "bash"
        - in: path
          name: container
          schema:
            type: string
          required: true
          description: name of the container
          example: "devtron"
      responses:
        200:
          description: session id
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TerminalMessage"
  /orchestrator/k8s/api-resources/{clusterId}:
    get:
      description: Get All api resources for given cluster Id
      parameters:
        - name: clusterId
          in: path
          description: cluster Id
          required: true
          schema:
            type: integer
            format: int64
      responses:
        "200":
          description: Successfully fetched All api resources for given cluster Id
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetAllApiResourcesResponse"
  /orchestrator/k8s/resource/list:
    post:
      description: this api will be used for fetching all kind of manifest.
      requestBody:
        description: json as request body
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceRequestObject'
      responses:
        '200':
          description: list response
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    type: array
                    description: app list
                    items:
                      $ref: '#/components/schemas/ClusterResourceListResponse'
  /orchestrator/k8s/resources/rotate:
    post:
      description: this api will be used to rotate pods for provided resources
      parameters:
        - in: query
          name: appId
          description: app id
          required: true
          schema:
            type: string
      requestBody:
        description: json as request body
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RotatePodRequest'
      responses:
        '200':
          description: response in array of each resource
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RotatePodResponse"
  /orchestrator/k8s/resources/apply:
    post:
      description: this api will be used to apply the resources in cluster
      requestBody:
        description: json as request body
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ApplyResourcesRequest'
      responses:
        '200':
          description: response in array of each resource
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/ApplyResourcesResponse"
components:
  schemas:
    TerminalMessage:
      type: object
      properties:
        Op:
          type: string
        Data:
          type: string
        SessionID:
          type: string
    ResourceRequestObject:
      type: object
      properties:
        appId:
          type: string
        clusterId:
          type: number
          description: clusterId is used when request is for direct cluster (when appId is not supplied)
        k8sRequest:
          $ref: '#/components/schemas/K8sRequestObject'
    K8sRequestObject:
      type: object
      properties:
        resourceIdentifier:
          type: object
          properties:
            groupVersionKind:
              type: object
              properties:
                Group:
                  type: string
                Version:
                  type: string
                Kind:
                  type: string
            namespace:
              type: string
            name:
              type: string
          required:
            - name
        podLogsRequest:
          type: object
          properties:
            containerName:
              type: string
        patch:
          type: string
    ResourceGetResponse:
      type: object
      properties:
        manifestResponse:
          $ref: '#/components/schemas/ManifestResponse'
        secretViewAccess:
          type: boolean
          description: >
            Indicates whether a user can see obscured secret values or not.
      required:
        - manifestResponse
        - secretViewAccess
    ManifestResponse:
      type: object
      properties:
        manifest:
          type: object
          properties:
            apiVersion:
              type: string
            data:
              type: object
              properties:
                envoy.yaml:
                  type: string
            kind:
              type: string
            metadata:
              type: object
              properties:
                annotations:
                  type: object
                  properties:
                    meta.helm.sh/release-name:
                      type: string
                    meta.helm.sh/release-namespace:
                      type: string
                creationTimestamp:
                  type: string
                  format: date-time
                labels:
                  type: object
                  properties:
                    app:
                      type: string
                    app.kubernetes.io/managed-by:
                      type: string
                    chart:
                      type: string
                    heritage:
                      type: string
                    release:
                      type: string
                name:
                  type: string
                namespace:
                  type: string
                resourceVersion:
                  type: string
                selfLink:
                  type: string
                uid:
                  type: string
    EventsResponseObject:
      type: object
      properties:
        events:
          type: object
          properties:
            metadata:
              type: object
              properties:
                selfLink:
                  type: string
                resourceVersion:
                  type: string
            items:
              type: array
              items:
                type: object
                properties:
                  metadata:
                    type: object
                    properties:
                      name:
                        type: string
                      namespace:
                        type: string
                      selfLink:
                        type: string
                      uid:
                        type: string
                      resourceVersion:
                        type: string
                      creationTimestamp:
                        type: string
                        format: date-time
                      managedFields:
                        type: array
                        items:
                          type: object
                          properties:
                            manager:
                              type: string
                            operation:
                              type: string
                            apiVersion:
                              type: string
                            time:
                              type: string
                              format: date-time
                  involvedObject:
                    type: object
                    properties:
                      kind:
                        type: string
                      namespace:
                        type: string
                      name:
                        type: string
                      uid:
                        type: string
                      apiVersion:
                        type: string
                      resourceVersion:
                        type: string
                  reason:
                    type: string
                  message:
                    type: string
                  source:
                    type: object
                    properties:
                      component:
                        type: string
                  firstTimestamp:
                    type: string
                    format: date-time
                  lastTimestamp:
                    type: string
                    format: date-time
                  count:
                    type: integer
                    format: int32
                  type:
                    type: string
                  eventTime:
                    type: string
                    format: nullable
                  reportingComponent:
                    type: string
                  reportingInstance:
                    type: string
    LogsResponseObject:
      type: object
      properties:
        logs:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
              type:
                type: string
              data:
                type: string
              time:
                type: string
    GetAllApiResourcesResponse:
      type: object
      properties:
        apiResources:
          type: array
          items:
            $ref: "#/components/schemas/K8sApiResource"
        allowedAll:
          type: boolean
          description: whether all api-resources allowed for this user
          example: true
          nullable: false
    K8sApiResource:
      type: object
      properties:
        gvk:
          $ref: '#/components/schemas/GroupVersionKind'
        namespaced:
          type: boolean
          description: whether this api resource is in namespaces scope or global
          example: true
          nullable: false
    GroupVersionKind:
      type: object
      properties:
        group:
          type: string
          description: group of the api-resource
          example: "apps"
          nullable: false
        version:
          type: string
          description: version of the api-resource
          example: "v1"
          nullable: false
        kind:
          type: string
          description: kind of the api-resource
          example: "pod"
          nullable: false
    ClusterResourceListResponse:
      type: object
      properties:
        headers:
          type: array
          items:
            type: string
        data:
          type: array
          items:
            type: object
            properties:
              header-name:
                type: string
                description: each object from data key contains the objects keys length is equal to headers length
    RotatePodRequest:
      type: object
      properties:
        clusterId:
          type: number
          description: cluster Id
          example: 1
          nullable: false
        resources:
          type: array
          items:
            type: object
            properties:
              groupVersionKind:
                type: object
                properties:
                  Group:
                    type: string
                  Version:
                    type: string
                  Kind:
                    type: string
              namespace:
                type: string
              name:
                type: string
            required:
              - name
    RotatePodResponse:
      type: object
      properties:
        containsError:
          type: boolean
          description: contains error
          example: true
        responses:
          type: array
          items:
            type: object
            properties:
              groupVersionKind:
                type: object
                properties:
                  Group:
                    type: string
                  Version:
                    type: string
                  Kind:
                    type: string
              namespace:
                type: string
              name:
                type: string
              errorResponse:
                type: string
    ApplyResourcesRequest:
      type: object
      properties:
        clusterId:
          type: number
          description: cluster Id
          example: 1
          nullable: false
        manifest:
          type: string
          description: manifest of the resources (yamls saparated by ---)
          example: ""
          nullable: false
    ApplyResourcesResponse:
      type: object
      properties:
        kind:
          type: string
          description: kind of the resource
          example: "pod"
          nullable: false
        name:
          type: string
          description: name of the resource
          example: "someName"
          nullable: false
        error:
          type: string
          description: error in the operation of this resource
          example: "someError"
          nullable: true
        isUpdate:
          type: boolean
          description: whether this resource was updated
          example: true
          nullable: false