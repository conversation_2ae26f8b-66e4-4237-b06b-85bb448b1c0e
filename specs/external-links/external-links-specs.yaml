openapi: "3.0.0"
info:
  version: 1.0.0
  title: External links APIs
paths:
  /orchestrator/external-link/tools:
    get:
      description: Get all available monitoring tools for external links
      responses:
        "200":
          description: Gives all monitoring tools
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/ExternalLinkMonitoringTool"
  /orchestrator/external-link:
    get:
      description: Get All active External links (If query parameter is not supplied
        then it will give all external links otherwise it will give external links
        mapped to that type and identifier)
      parameters:
        - in: query
          name: clusterId
          example: 1
          description: clusterId
          required: false
          allowEmptyValue: false
          schema:
            type: number
        - in: query
          name: identifier
          example: '1'
          description: link type identifier
          required: false
          allowEmptyValue: false
          schema:
            type: string
        - in: query
          name: type
          example: devtron-app
          description: link type,only [devtron-app,devtron-installed-app,external-helm-app] allowed
          required: false
          allowEmptyValue: false
          schema:
            type: string
      responses:
        "200":
          description: Successfully fetched external links
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/ExternalLink"
    post:
      description: Create external links (id should be zero in externalLink object)
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: "#/components/schemas/ExternalLink"
      responses:
        "200":
          description: External links creation response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ActionResponse"
    put:
      description: update external link
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ExternalLink"
      responses:
        "200":
          description: External link update response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ActionResponse"
    delete:
      description: delete external link
      parameters:
        - in: query
          name: id
          example: 1
          description: external link Id
          required: true
          allowEmptyValue: false
          schema:
            type: integer
      responses:
        "200":
          description: External link delete response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ActionResponse"


# Components
components:
  schemas:
    ExternalLinkMonitoringTool:
      type: object
      properties:
        id:
          type: integer
          description: Id of monitoring tool
          example: 1
        name:
          type: string
          description: name of monitoring tool
          example: "grafana"
        icon:
          type: string
          description: url of monitoring tool icon
        category:
          type: integer
          description: category of monitoring tool
          example: 1
    ExternalLink:
      type: object
      properties:
        id:
          type: integer
          description: Id of external link
          example: 1
        monitoringToolId:
          type: integer
          description: Id of monitoring tool, to which this is mapped
          example: 1
        name:
          type: string
          description: external link name
          example: "some name"
        url:
          type: string
          description: url given to this external link
          example: "some url"
        type:
          type: string
          description: type of link (appLevel/clusterLevel)
        identifiers:
          type: array
          description: link identifiers to which this url is applied e.g {"type":"devtron-app","identifier":"1"}
          items:
            "$ref": "#/components/schemas/LinkIdentifier"
        updatedOn:
          type: string
          description: updated on
        isEditable:
          type: boolean
          description: explains link is editable by app admin or not
        description:
          type: string
          description: description about the external link
    ActionResponse:
      type: object
      properties:
        success:
          type: boolean
          description: success or failure
          example: true
    LinkIdentifier:
      type: object
      properties:
        type:
          type: string
          description: type of link identifier[devtron-app,devtron-installed-app,external-helm-app,cluster]
          example: cluster
        identifier:
          type: string
          description: identifier for this type,(for cluster type this will be empty)
          example: ''
        clusterId:
          type: integer
          description: cluster-id for which you want to configure the app level external-link
