openapi: "3.0.0"
info:
  version: 1.0.0
  title: Bulk Actions - Hibernate, UnHibernate, Deploy Latest Builds
servers:
  - url: http://localhost:3000/orchestrator/batch
paths:
  /v1beta1/hibernate:
    post:
      description: Bulk Hibernate all apps for specific environment
      operationId: BulkHibernate
      requestBody:
        description: bulk hibernate
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkActionPayload'
      responses:
        '200':
          description: Successfully hibernated all impacted apps.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkActionPayload'
        '400':
          description: Bad Request. Validation error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /v1beta1/unhibernate:
    post:
      description: Bulk Unhibernate all apps for specific environment
      operationId: BulkUnhibernate
      requestBody:
        description: bulk unhibernate
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkActionPayload'
      responses:
        '200':
          description: Successfully unhibernated all impacted apps.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkActionPayload'
        '400':
          description: Bad Request. Validation error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /v1beta1/deploy:
    post:
      description: Bulk Deploy all apps to the latest build image for specific environment
      operationId: BulkDeploy
      requestBody:
        description: bulk deploy
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkActionPayload'
      responses:
        '200':
          description: Successfully deploy all impacted apps.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkActionPayload'
        '400':
          description: Bad Request. Validation error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'


components:
  schemas:
    BulkActionPayload:
      type: object
      required: envId
      properties:
        appIdIncludes:
          type: array
          items:
            type: integer
        appIdExcludes:
          type: array
          items:
            type: integer
        envId:
          type: integer
          description: environment id

    IdsIncludesExcludes:
      type: object
      properties:
        ids:
          type: array
          items:
            type: integer
          description: app ids

    Error:
      required:
        - code
        - message
      properties:
        code:
          type: integer
          description: Error code
        message:
          type: string
          description: Error message