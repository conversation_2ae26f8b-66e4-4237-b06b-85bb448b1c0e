/*
 * Copyright (c) 2020-2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package appList

import (
	"github.com/devtron-labs/devtron/api/restHandler/app/appList"
	"github.com/gorilla/mux"
)

type AppFilteringRouter interface {
	InitAppFilteringRouter(helmRouter *mux.Router)
}

type AppFilteringRouterImpl struct {
	appFilteringRestHandler appList.AppFilteringRestHandler
}

func NewAppFilteringRouterImpl(appFilteringRestHandler appList.AppFilteringRestHandler) *AppFilteringRouterImpl {
	router := &AppFilteringRouterImpl{
		appFilteringRestHandler: appFilteringRestHandler,
	}
	return router
}

func (router AppFilteringRouterImpl) InitAppFilteringRouter(AppFilteringRouter *mux.Router) {
	AppFilteringRouter.Path("/autocomplete").
		HandlerFunc(router.appFilteringRestHandler.GetClusterTeamAndEnvListForAutocomplete).Methods("GET")
}
