/*
Devtron Labs

No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

API version: 1.0.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package openapi

import (
	"encoding/json"
)

// HelmAppDeploymentManifestDetail struct for HelmAppDeploymentManifestDetail
type HelmAppDeploymentManifestDetail struct {
	// manifest of deployment
	Manifest *string `json:"manifest,omitempty"`
	// values YAML of deployment
	ValuesYaml *string `json:"valuesYaml,omitempty"`
}

// NewHelmAppDeploymentManifestDetail instantiates a new HelmAppDeploymentManifestDetail object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewHelmAppDeploymentManifestDetail() *HelmAppDeploymentManifestDetail {
	this := HelmAppDeploymentManifestDetail{}
	return &this
}

// NewHelmAppDeploymentManifestDetailWithDefaults instantiates a new HelmAppDeploymentManifestDetail object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewHelmAppDeploymentManifestDetailWithDefaults() *HelmAppDeploymentManifestDetail {
	this := HelmAppDeploymentManifestDetail{}
	return &this
}

// GetManifest returns the Manifest field value if set, zero value otherwise.
func (o *HelmAppDeploymentManifestDetail) GetManifest() string {
	if o == nil || o.Manifest == nil {
		var ret string
		return ret
	}
	return *o.Manifest
}

// GetManifestOk returns a tuple with the Manifest field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *HelmAppDeploymentManifestDetail) GetManifestOk() (*string, bool) {
	if o == nil || o.Manifest == nil {
		return nil, false
	}
	return o.Manifest, true
}

// HasManifest returns a boolean if a field has been set.
func (o *HelmAppDeploymentManifestDetail) HasManifest() bool {
	if o != nil && o.Manifest != nil {
		return true
	}

	return false
}

// SetManifest gets a reference to the given string and assigns it to the Manifest field.
func (o *HelmAppDeploymentManifestDetail) SetManifest(v string) {
	o.Manifest = &v
}

// GetValuesYaml returns the ValuesYaml field value if set, zero value otherwise.
func (o *HelmAppDeploymentManifestDetail) GetValuesYaml() string {
	if o == nil || o.ValuesYaml == nil {
		var ret string
		return ret
	}
	return *o.ValuesYaml
}

// GetValuesYamlOk returns a tuple with the ValuesYaml field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *HelmAppDeploymentManifestDetail) GetValuesYamlOk() (*string, bool) {
	if o == nil || o.ValuesYaml == nil {
		return nil, false
	}
	return o.ValuesYaml, true
}

// HasValuesYaml returns a boolean if a field has been set.
func (o *HelmAppDeploymentManifestDetail) HasValuesYaml() bool {
	if o != nil && o.ValuesYaml != nil {
		return true
	}

	return false
}

// SetValuesYaml gets a reference to the given string and assigns it to the ValuesYaml field.
func (o *HelmAppDeploymentManifestDetail) SetValuesYaml(v string) {
	o.ValuesYaml = &v
}

func (o HelmAppDeploymentManifestDetail) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Manifest != nil {
		toSerialize["manifest"] = o.Manifest
	}
	if o.ValuesYaml != nil {
		toSerialize["valuesYaml"] = o.ValuesYaml
	}
	return json.Marshal(toSerialize)
}

type NullableHelmAppDeploymentManifestDetail struct {
	value *HelmAppDeploymentManifestDetail
	isSet bool
}

func (v NullableHelmAppDeploymentManifestDetail) Get() *HelmAppDeploymentManifestDetail {
	return v.value
}

func (v *NullableHelmAppDeploymentManifestDetail) Set(val *HelmAppDeploymentManifestDetail) {
	v.value = val
	v.isSet = true
}

func (v NullableHelmAppDeploymentManifestDetail) IsSet() bool {
	return v.isSet
}

func (v *NullableHelmAppDeploymentManifestDetail) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableHelmAppDeploymentManifestDetail(val *HelmAppDeploymentManifestDetail) *NullableHelmAppDeploymentManifestDetail {
	return &NullableHelmAppDeploymentManifestDetail{value: val, isSet: true}
}

func (v NullableHelmAppDeploymentManifestDetail) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableHelmAppDeploymentManifestDetail) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
