/*
Devtron Labs

No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

API version: 1.0.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package openapi

import (
	"encoding/json"
)

// HelmAppListRequest struct for HelmAppListRequest
type HelmAppListRequest struct {
	// cluster ids
	ClusterIds *[]int64 `json:"clusterIds,omitempty"`
}

// NewHelmAppListRequest instantiates a new HelmAppListRequest object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewHelmAppListRequest() *HelmAppListRequest {
	this := HelmAppListRequest{}
	return &this
}

// NewHelmAppListRequestWithDefaults instantiates a new HelmAppListRequest object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewHelmAppListRequestWithDefaults() *HelmAppListRequest {
	this := HelmAppListRequest{}
	return &this
}

// GetClusterIds returns the ClusterIds field value if set, zero value otherwise.
func (o *HelmAppListRequest) GetClusterIds() []int64 {
	if o == nil || o.ClusterIds == nil {
		var ret []int64
		return ret
	}
	return *o.ClusterIds
}

// GetClusterIdsOk returns a tuple with the ClusterIds field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *HelmAppListRequest) GetClusterIdsOk() (*[]int64, bool) {
	if o == nil || o.ClusterIds == nil {
		return nil, false
	}
	return o.ClusterIds, true
}

// HasClusterIds returns a boolean if a field has been set.
func (o *HelmAppListRequest) HasClusterIds() bool {
	if o != nil && o.ClusterIds != nil {
		return true
	}

	return false
}

// SetClusterIds gets a reference to the given []int64 and assigns it to the ClusterIds field.
func (o *HelmAppListRequest) SetClusterIds(v []int64) {
	o.ClusterIds = &v
}

func (o HelmAppListRequest) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.ClusterIds != nil {
		toSerialize["clusterIds"] = o.ClusterIds
	}
	return json.Marshal(toSerialize)
}

type NullableHelmAppListRequest struct {
	value *HelmAppListRequest
	isSet bool
}

func (v NullableHelmAppListRequest) Get() *HelmAppListRequest {
	return v.value
}

func (v *NullableHelmAppListRequest) Set(val *HelmAppListRequest) {
	v.value = val
	v.isSet = true
}

func (v NullableHelmAppListRequest) IsSet() bool {
	return v.isSet
}

func (v *NullableHelmAppListRequest) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableHelmAppListRequest(val *HelmAppListRequest) *NullableHelmAppListRequest {
	return &NullableHelmAppListRequest{value: val, isSet: true}
}

func (v NullableHelmAppListRequest) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableHelmAppListRequest) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
