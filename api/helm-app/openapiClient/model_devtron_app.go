/*
Devtron Labs

No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

API version: 1.0.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package openapi

import (
	"encoding/json"
)

// DevtronApp struct for DevtronApp
type DevtronApp struct {
	// name of the helm application/helm release name
	AppName *string `json:"appName,omitempty"`
	// unique identifier for app
	AppId *string `json:"appId,omitempty"`
	// unique identifier for the project
	ProjectId          *int32                  `json:"projectId,omitempty"`
	EnvironmentDetails *[]AppEnvironmentDetail `json:"environmentDetails,omitempty"`
}

// NewDevtronApp instantiates a new DevtronApp object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewDevtronApp() *DevtronApp {
	this := DevtronApp{}
	return &this
}

// NewDevtronAppWithDefaults instantiates a new DevtronApp object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewDevtronAppWithDefaults() *DevtronApp {
	this := DevtronApp{}
	return &this
}

// GetAppName returns the AppName field value if set, zero value otherwise.
func (o *DevtronApp) GetAppName() string {
	if o == nil || o.AppName == nil {
		var ret string
		return ret
	}
	return *o.AppName
}

// GetAppNameOk returns a tuple with the AppName field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DevtronApp) GetAppNameOk() (*string, bool) {
	if o == nil || o.AppName == nil {
		return nil, false
	}
	return o.AppName, true
}

// HasAppName returns a boolean if a field has been set.
func (o *DevtronApp) HasAppName() bool {
	if o != nil && o.AppName != nil {
		return true
	}

	return false
}

// SetAppName gets a reference to the given string and assigns it to the AppName field.
func (o *DevtronApp) SetAppName(v string) {
	o.AppName = &v
}

// GetAppId returns the AppId field value if set, zero value otherwise.
func (o *DevtronApp) GetAppId() string {
	if o == nil || o.AppId == nil {
		var ret string
		return ret
	}
	return *o.AppId
}

// GetAppIdOk returns a tuple with the AppId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DevtronApp) GetAppIdOk() (*string, bool) {
	if o == nil || o.AppId == nil {
		return nil, false
	}
	return o.AppId, true
}

// HasAppId returns a boolean if a field has been set.
func (o *DevtronApp) HasAppId() bool {
	if o != nil && o.AppId != nil {
		return true
	}

	return false
}

// SetAppId gets a reference to the given string and assigns it to the AppId field.
func (o *DevtronApp) SetAppId(v string) {
	o.AppId = &v
}

// GetProjectId returns the ProjectId field value if set, zero value otherwise.
func (o *DevtronApp) GetProjectId() int32 {
	if o == nil || o.ProjectId == nil {
		var ret int32
		return ret
	}
	return *o.ProjectId
}

// GetProjectIdOk returns a tuple with the ProjectId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DevtronApp) GetProjectIdOk() (*int32, bool) {
	if o == nil || o.ProjectId == nil {
		return nil, false
	}
	return o.ProjectId, true
}

// HasProjectId returns a boolean if a field has been set.
func (o *DevtronApp) HasProjectId() bool {
	if o != nil && o.ProjectId != nil {
		return true
	}

	return false
}

// SetProjectId gets a reference to the given int32 and assigns it to the ProjectId field.
func (o *DevtronApp) SetProjectId(v int32) {
	o.ProjectId = &v
}

// GetEnvironmentDetails returns the EnvironmentDetails field value if set, zero value otherwise.
func (o *DevtronApp) GetEnvironmentDetails() []AppEnvironmentDetail {
	if o == nil || o.EnvironmentDetails == nil {
		var ret []AppEnvironmentDetail
		return ret
	}
	return *o.EnvironmentDetails
}

// GetEnvironmentDetailsOk returns a tuple with the EnvironmentDetails field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DevtronApp) GetEnvironmentDetailsOk() (*[]AppEnvironmentDetail, bool) {
	if o == nil || o.EnvironmentDetails == nil {
		return nil, false
	}
	return o.EnvironmentDetails, true
}

// HasEnvironmentDetails returns a boolean if a field has been set.
func (o *DevtronApp) HasEnvironmentDetails() bool {
	if o != nil && o.EnvironmentDetails != nil {
		return true
	}

	return false
}

// SetEnvironmentDetails gets a reference to the given []AppEnvironmentDetail and assigns it to the EnvironmentDetails field.
func (o *DevtronApp) SetEnvironmentDetails(v []AppEnvironmentDetail) {
	o.EnvironmentDetails = &v
}

func (o DevtronApp) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.AppName != nil {
		toSerialize["appName"] = o.AppName
	}
	if o.AppId != nil {
		toSerialize["appId"] = o.AppId
	}
	if o.ProjectId != nil {
		toSerialize["projectId"] = o.ProjectId
	}
	if o.EnvironmentDetails != nil {
		toSerialize["environmentDetails"] = o.EnvironmentDetails
	}
	return json.Marshal(toSerialize)
}

type NullableDevtronApp struct {
	value *DevtronApp
	isSet bool
}

func (v NullableDevtronApp) Get() *DevtronApp {
	return v.value
}

func (v *NullableDevtronApp) Set(val *DevtronApp) {
	v.value = val
	v.isSet = true
}

func (v NullableDevtronApp) IsSet() bool {
	return v.isSet
}

func (v *NullableDevtronApp) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableDevtronApp(val *DevtronApp) *NullableDevtronApp {
	return &NullableDevtronApp{value: val, isSet: true}
}

func (v NullableDevtronApp) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableDevtronApp) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
