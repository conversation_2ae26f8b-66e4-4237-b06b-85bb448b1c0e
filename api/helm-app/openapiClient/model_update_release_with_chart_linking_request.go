/*
Devtron Labs

No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

API version: 1.0.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package openapi

import (
	"encoding/json"
)

// UpdateReleaseWithChartLinkingRequest struct for UpdateReleaseWithChartLinkingRequest
type UpdateReleaseWithChartLinkingRequest struct {
	// helm app id
	AppId *string `json:"appId,omitempty"`
	// updated values yaml string
	ValuesYaml *string `json:"valuesYaml,omitempty"`
	// app store application version Id
	AppStoreApplicationVersionId *float32 `json:"appStoreApplicationVersionId,omitempty"`
	// Reference value Id of selected chart values
	ReferenceValueId *float32 `json:"referenceValueId,omitempty"`
	// Reference value Kind of selected chart values \"oneof=DEFAULT TEMPLATE DEPLOYED EXISTING\" (can be null)
	ReferenceValueKind *string `json:"referenceValueKind,omitempty"`
}

// NewUpdateReleaseWithChartLinkingRequest instantiates a new UpdateReleaseWithChartLinkingRequest object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewUpdateReleaseWithChartLinkingRequest() *UpdateReleaseWithChartLinkingRequest {
	this := UpdateReleaseWithChartLinkingRequest{}
	return &this
}

// NewUpdateReleaseWithChartLinkingRequestWithDefaults instantiates a new UpdateReleaseWithChartLinkingRequest object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewUpdateReleaseWithChartLinkingRequestWithDefaults() *UpdateReleaseWithChartLinkingRequest {
	this := UpdateReleaseWithChartLinkingRequest{}
	return &this
}

// GetAppId returns the AppId field value if set, zero value otherwise.
func (o *UpdateReleaseWithChartLinkingRequest) GetAppId() string {
	if o == nil || o.AppId == nil {
		var ret string
		return ret
	}
	return *o.AppId
}

// GetAppIdOk returns a tuple with the AppId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UpdateReleaseWithChartLinkingRequest) GetAppIdOk() (*string, bool) {
	if o == nil || o.AppId == nil {
		return nil, false
	}
	return o.AppId, true
}

// HasAppId returns a boolean if a field has been set.
func (o *UpdateReleaseWithChartLinkingRequest) HasAppId() bool {
	if o != nil && o.AppId != nil {
		return true
	}

	return false
}

// SetAppId gets a reference to the given string and assigns it to the AppId field.
func (o *UpdateReleaseWithChartLinkingRequest) SetAppId(v string) {
	o.AppId = &v
}

// GetValuesYaml returns the ValuesYaml field value if set, zero value otherwise.
func (o *UpdateReleaseWithChartLinkingRequest) GetValuesYaml() string {
	if o == nil || o.ValuesYaml == nil {
		var ret string
		return ret
	}
	return *o.ValuesYaml
}

// GetValuesYamlOk returns a tuple with the ValuesYaml field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UpdateReleaseWithChartLinkingRequest) GetValuesYamlOk() (*string, bool) {
	if o == nil || o.ValuesYaml == nil {
		return nil, false
	}
	return o.ValuesYaml, true
}

// HasValuesYaml returns a boolean if a field has been set.
func (o *UpdateReleaseWithChartLinkingRequest) HasValuesYaml() bool {
	if o != nil && o.ValuesYaml != nil {
		return true
	}

	return false
}

// SetValuesYaml gets a reference to the given string and assigns it to the ValuesYaml field.
func (o *UpdateReleaseWithChartLinkingRequest) SetValuesYaml(v string) {
	o.ValuesYaml = &v
}

// GetAppStoreApplicationVersionId returns the AppStoreApplicationVersionId field value if set, zero value otherwise.
func (o *UpdateReleaseWithChartLinkingRequest) GetAppStoreApplicationVersionId() float32 {
	if o == nil || o.AppStoreApplicationVersionId == nil {
		var ret float32
		return ret
	}
	return *o.AppStoreApplicationVersionId
}

// GetAppStoreApplicationVersionIdOk returns a tuple with the AppStoreApplicationVersionId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UpdateReleaseWithChartLinkingRequest) GetAppStoreApplicationVersionIdOk() (*float32, bool) {
	if o == nil || o.AppStoreApplicationVersionId == nil {
		return nil, false
	}
	return o.AppStoreApplicationVersionId, true
}

// HasAppStoreApplicationVersionId returns a boolean if a field has been set.
func (o *UpdateReleaseWithChartLinkingRequest) HasAppStoreApplicationVersionId() bool {
	if o != nil && o.AppStoreApplicationVersionId != nil {
		return true
	}

	return false
}

// SetAppStoreApplicationVersionId gets a reference to the given float32 and assigns it to the AppStoreApplicationVersionId field.
func (o *UpdateReleaseWithChartLinkingRequest) SetAppStoreApplicationVersionId(v float32) {
	o.AppStoreApplicationVersionId = &v
}

// GetReferenceValueId returns the ReferenceValueId field value if set, zero value otherwise.
func (o *UpdateReleaseWithChartLinkingRequest) GetReferenceValueId() float32 {
	if o == nil || o.ReferenceValueId == nil {
		var ret float32
		return ret
	}
	return *o.ReferenceValueId
}

// GetReferenceValueIdOk returns a tuple with the ReferenceValueId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UpdateReleaseWithChartLinkingRequest) GetReferenceValueIdOk() (*float32, bool) {
	if o == nil || o.ReferenceValueId == nil {
		return nil, false
	}
	return o.ReferenceValueId, true
}

// HasReferenceValueId returns a boolean if a field has been set.
func (o *UpdateReleaseWithChartLinkingRequest) HasReferenceValueId() bool {
	if o != nil && o.ReferenceValueId != nil {
		return true
	}

	return false
}

// SetReferenceValueId gets a reference to the given float32 and assigns it to the ReferenceValueId field.
func (o *UpdateReleaseWithChartLinkingRequest) SetReferenceValueId(v float32) {
	o.ReferenceValueId = &v
}

// GetReferenceValueKind returns the ReferenceValueKind field value if set, zero value otherwise.
func (o *UpdateReleaseWithChartLinkingRequest) GetReferenceValueKind() string {
	if o == nil || o.ReferenceValueKind == nil {
		var ret string
		return ret
	}
	return *o.ReferenceValueKind
}

// GetReferenceValueKindOk returns a tuple with the ReferenceValueKind field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UpdateReleaseWithChartLinkingRequest) GetReferenceValueKindOk() (*string, bool) {
	if o == nil || o.ReferenceValueKind == nil {
		return nil, false
	}
	return o.ReferenceValueKind, true
}

// HasReferenceValueKind returns a boolean if a field has been set.
func (o *UpdateReleaseWithChartLinkingRequest) HasReferenceValueKind() bool {
	if o != nil && o.ReferenceValueKind != nil {
		return true
	}

	return false
}

// SetReferenceValueKind gets a reference to the given string and assigns it to the ReferenceValueKind field.
func (o *UpdateReleaseWithChartLinkingRequest) SetReferenceValueKind(v string) {
	o.ReferenceValueKind = &v
}

func (o UpdateReleaseWithChartLinkingRequest) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.AppId != nil {
		toSerialize["appId"] = o.AppId
	}
	if o.ValuesYaml != nil {
		toSerialize["valuesYaml"] = o.ValuesYaml
	}
	if o.AppStoreApplicationVersionId != nil {
		toSerialize["appStoreApplicationVersionId"] = o.AppStoreApplicationVersionId
	}
	if o.ReferenceValueId != nil {
		toSerialize["referenceValueId"] = o.ReferenceValueId
	}
	if o.ReferenceValueKind != nil {
		toSerialize["referenceValueKind"] = o.ReferenceValueKind
	}
	return json.Marshal(toSerialize)
}

type NullableUpdateReleaseWithChartLinkingRequest struct {
	value *UpdateReleaseWithChartLinkingRequest
	isSet bool
}

func (v NullableUpdateReleaseWithChartLinkingRequest) Get() *UpdateReleaseWithChartLinkingRequest {
	return v.value
}

func (v *NullableUpdateReleaseWithChartLinkingRequest) Set(val *UpdateReleaseWithChartLinkingRequest) {
	v.value = val
	v.isSet = true
}

func (v NullableUpdateReleaseWithChartLinkingRequest) IsSet() bool {
	return v.isSet
}

func (v *NullableUpdateReleaseWithChartLinkingRequest) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableUpdateReleaseWithChartLinkingRequest(val *UpdateReleaseWithChartLinkingRequest) *NullableUpdateReleaseWithChartLinkingRequest {
	return &NullableUpdateReleaseWithChartLinkingRequest{value: val, isSet: true}
}

func (v NullableUpdateReleaseWithChartLinkingRequest) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableUpdateReleaseWithChartLinkingRequest) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


